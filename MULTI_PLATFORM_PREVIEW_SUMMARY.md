# Multi-Platform Preview Tool Summary

## Overview

The preview tool has been updated with a completely rewritten `_create_standardized_fields_with_data` function that dynamically supports all advertising platforms by reading their JSON schema files and outputting all fields in the `■ key: value` format.

## Key Improvements

### 1. Dynamic Platform Support
- **Reads JSON schemas**: Automatically processes all fields from platform JSON files
- **No hardcoded fields**: Supports any platform by reading its schema structure
- **Extensible**: New platforms can be added by simply adding their JSON schema file

### 2. Comprehensive Field Processing
- **All fields included**: Processes every field defined in the platform's JSON schema
- **Nested structure support**: Handles complex structures like Meta's carousel ads and feed ads
- **Flexible formatting**: Adapts to different field types and configurations

### 3. Enhanced Data Presentation
- **Actual data display**: Shows extracted content when available
- **Smart placeholders**: Provides appropriate placeholders for missing data
- **Character validation**: Includes character count with validation status
- **Type-aware formatting**: Handles strings, arrays, and nested objects appropriately

## Technical Implementation

### Core Function: `_create_standardized_fields_with_data`

```python
def _create_standardized_fields_with_data(self, schema_fields: Dict[str, Any], data: Dict[str, Any]) -> Dict[str, str]:
    """
    Create output fields with actual extracted data values for all platforms.
    
    This function dynamically processes all fields from the platform's JSON schema
    and formats them with actual data or appropriate placeholders.
    """
```

### Key Methods

#### 1. `_is_nested_structure(field_config)`
- Detects nested structures like carousel cards or feed ads
- Checks for "cards" key or nested object patterns

#### 2. `_process_nested_field(field_name, field_config, data)`
- Handles complex nested structures
- Processes carousel ads with multiple cards
- Manages feed ads with sub-fields

#### 3. `_format_field_with_data(field_name, field_config, data)`
- Formats individual fields with actual data or placeholders
- Handles different data types (strings, arrays, objects)
- Adds character count validation

#### 4. `_get_char_range_info(field_config, actual_count)`
- Provides character count with validation status
- Shows ✓ for valid counts, ⚠️ for violations
- Formats as `[actual/min–max chars status]`

#### 5. `_create_field_placeholder(field_config, description, options)`
- Creates appropriate placeholders for missing data
- Uses field options, descriptions, or default placeholders

## Output Format Examples

### LINE Ads
```
LINE
■ キャンペーン: 美容商品春キャンペーン [18/15–30 chars ✓]
■ グループ: 20代女性向け美容商品 [12/10–25 chars ✓]
■ 配信条件／配信ポイント: 20-30歳女性、東京・大阪エリア、美容・健康に興味 [28/50–100 chars ✓]
■ 年齢: 20-30
■ 性別: 女性
■ エリア: 東京・大阪
■ 見出し: 新商品登場！美しさを引き出す [15/max 20 chars ✓]
■ 説明文: 革新的な美容商品で、あなたの美しさを最大限に引き出します [32/max 75 chars ✓]
```

### Meta (Instagram/Facebook)
```
Meta
■ キャンペーン名: 美容商品Metaキャンペーン [14/15–30 chars ✓]
■ キャンペーンの目的: コンバージョン
■ 広告セット名: 美容セット1, 美容セット2 [13/10–25 chars ✓]
■ 配信条件／配信ポイント: 20-30歳女性, 美容興味 [15/50–100 chars ✓]
■ 性別: 女性
■ 年齢: 20-30
■ エリア: 日本全国
■ 配置場所: Meta全体
■ フィード広告・ストーリー広告 - 見出し: 美しさを引き出す新商品 [12/max 25 chars ✓]
■ フィード広告・ストーリー広告 - テキスト: 革新的な美容商品で、あなたの美しさを最大限に [25/max 125 chars ✓]
■ フィード広告・ストーリー広告 - URL: https://example.com/beauty
```

### YouTube Ads
```
YouTube
■ キャンペーン名: 美容商品YouTubeキャンペーン [16/15–30 chars ✓]
■ キャンペーンの目的: ブランド認知度とリーチ
■ 広告グループ名: 美容動画1, 美容動画2 [11/10–25 chars ✓]
■ ターゲティング: 20-30歳女性、美容関連興味 [15/50–100 chars ✓]
■ 年齢: 20-30
■ 性別: 女性
■ 地域: 日本
■ 動画広告 - 見出し: 美容商品紹介動画 [9/max 15 chars ✓]
■ 動画広告 - 説明: 革新的な美容商品の魅力をご紹介 [17/max 80 chars ✓]
■ 動画広告 - 動画URL: https://youtube.com/watch?v=example
```

## Platform Support

### Currently Supported Platforms
- **LINE Ads**: Complete field support
- **Meta (Instagram/Facebook)**: Including carousel and feed ads
- **YouTube Ads**: Video ad formats
- **Google Search Ads**: Text ad formats
- **Google Display Ads**: Display ad formats
- **Google Demand Gen Ads**: Demand generation formats
- **P-Max**: Performance Max campaigns
- **Ad Extensions**: Various extension types

### Nested Structure Support
- **Carousel Ads**: Multiple card structures
- **Feed Ads**: Nested field hierarchies
- **Video Ads**: Media-specific fields
- **Extension Groups**: Multiple extension types

## Benefits

### 1. Scalability
- **Easy platform addition**: Just add JSON schema file
- **No code changes**: New platforms work automatically
- **Consistent format**: Same ■ key: value structure across all platforms

### 2. Accuracy
- **Real data extraction**: Uses LLM to extract actual content
- **Validation feedback**: Shows character count compliance
- **Complete coverage**: Includes all platform-specific fields

### 3. User Experience
- **Clear formatting**: Easy to read and understand
- **Visual validation**: ✓ and ⚠️ indicators for compliance
- **Comprehensive preview**: Shows exactly what will be in Excel file

### 4. Maintainability
- **Single function**: All platform logic in one place
- **Data-driven**: Behavior controlled by JSON schemas
- **Flexible**: Adapts to schema changes automatically

## Future Enhancements

### Potential Improvements
1. **Field grouping**: Group related fields for better organization
2. **Conditional fields**: Show/hide fields based on other field values
3. **Field validation**: Real-time validation of field content
4. **Custom formatting**: Platform-specific formatting rules
5. **Field dependencies**: Handle interdependent field relationships

### Extension Points
- **Custom field processors**: For special field types
- **Validation rules**: Platform-specific validation logic
- **Formatting templates**: Customizable output formats
- **Field transformations**: Data transformation pipelines

## Conclusion

The updated `_create_standardized_fields_with_data` function provides:
- ✅ **Universal platform support** through dynamic JSON schema processing
- ✅ **Complete field coverage** including nested structures
- ✅ **Actual data display** with smart placeholder fallbacks
- ✅ **Character validation** with visual status indicators
- ✅ **Consistent formatting** across all platforms
- ✅ **Easy extensibility** for new platforms and field types

This implementation ensures the preview tool can handle any advertising platform by simply reading its JSON schema configuration, making it truly scalable and maintainable.

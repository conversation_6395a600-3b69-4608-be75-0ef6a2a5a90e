# Preview Ad Content Tool

## Overview

The `preview_ad_content` tool is a new LLM function calling tool that generates a preview of extracted ad content for the LINE platform. It allows users to review and refine advertising content before exporting it to an Excel file.

## Features

- **Data Extraction**: Extracts advertising content from conversation history using the same logic as the `create_excel_file` tool
- **Human-Readable Preview**: Formats extracted data in a structured, easy-to-read format
- **Validation**: Validates that sufficient advertising content exists in the conversation
- **Character Limit Checking**: Shows character counts and warns about limit violations
- **Completion Tracking**: Displays completion percentage and missing fields
- **LINE Ads Focus**: Specifically designed for LINE Ads platform

## Tool Specification

### Name
`preview_ad_content`

### Description
Generates a preview of extracted ad content for the LINE platform, formatted in a human-readable structure based on conversation history.

### Parameters
```json
{
  "type": "object",
  "properties": {
    "platform": {
      "type": "string",
      "description": "The advertising platform. Must be 'LINE Ads' for this tool.",
      "enum": ["LINE Ads"]
    }
  },
  "required": ["platform"]
}
```

### Return Value
```json
{
  "preview_content": "Formatted preview string",
  "platform": "LINE Ads",
  "extraction_status": "success"
}
```

## Usage Example

### Input
```json
{
  "platform": "LINE Ads"
}
```

### Output
The tool returns a formatted preview showing:
- **Campaign Data**: All extracted fields with their values
- **Field Descriptions**: Explanations of what each field represents
- **Character Limits**: Current character count vs. maximum allowed
- **Completion Status**: Percentage of fields completed
- **Missing Fields**: List of any incomplete fields
- **Next Steps**: Guidance on what to do next

## Integration

### Dependencies
The tool reuses existing system components:
- `ExcelManager`: For loading platform schemas and format files
- `PromptGenerator`: For generating data extraction prompts
- `PromptService`: For system prompt management
- LLM Client: For extracting data from conversation history

### File Structure
```
app/src/services/llm/tools/
├── preview_tool.py          # Main tool implementation
├── __init__.py             # Updated to include PreviewTool
└── ...
```

### Registration
The tool is automatically registered in the tools module and can be imported as:
```python
from app.src.services.llm.tools import PreviewTool
```

## Implementation Details

### Key Methods

1. **`execute(platform, **kwargs)`**: Main execution method
   - Validates platform (must be "LINE Ads")
   - Extracts conversation context
   - Validates advertising content exists
   - Extracts campaign data using LLM
   - Formats preview content

2. **`_extract_campaign_data(platform, messages)`**: Data extraction
   - Loads LINE Ads format schema
   - Generates extraction prompt
   - Calls LLM to extract structured data
   - Returns JSON with data and missing fields

3. **`_format_preview_content(platform, campaign_data)`**: Preview formatting
   - Creates human-readable preview
   - Shows field descriptions and constraints
   - Indicates completion status
   - Provides next step guidance

4. **`_has_advertising_content(messages)`**: Content validation
   - Checks for advertising-related keywords
   - Ensures sufficient content exists for extraction

### Error Handling
- **Platform Validation**: Only accepts "LINE Ads"
- **Content Validation**: Ensures advertising content exists
- **JSON Parsing**: Handles malformed LLM responses
- **Missing Data**: Gracefully handles incomplete extractions

## Testing

A comprehensive test suite is included in `test_preview_tool.py` that covers:
- Tool property validation
- Successful execution with mock data
- Error handling for unsupported platforms
- Error handling for missing advertising content

Run tests with:
```bash
python test_preview_tool.py
```

## Benefits

1. **User Experience**: Allows users to review content before Excel creation
2. **Quality Control**: Helps identify missing or problematic content
3. **Efficiency**: Reduces need to recreate Excel files due to content issues
4. **Transparency**: Shows exactly what data will be included in the Excel file
5. **Guidance**: Provides clear next steps for users

## Future Enhancements

Potential improvements could include:
- Support for additional platforms beyond LINE Ads
- Interactive editing capabilities
- Content suggestions for missing fields
- Integration with content validation rules
- Export to different preview formats

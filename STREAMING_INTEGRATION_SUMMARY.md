# Preview Tool Streaming Integration Summary

## Overview

The preview tool has been successfully integrated into the streaming response system with the following key features:

1. **Streaming Response**: Preview content is streamed to users in real-time
2. **No Follow-up Response**: When preview tool executes, no additional LLM response is generated
3. **Multi-Platform Support**: Works with all advertising platforms
4. **Standardized Format**: Consistent output format across platforms

## Implementation Details

### 1. Preview Tool Changes

**File**: `app/src/services/llm/tools/preview_tool.py`

- **Return Type**: Changed to return `{"type": "stream_response", ...}` instead of regular response
- **Streaming Generator**: Added `_create_stream_generator()` method for line-by-line streaming
- **Multi-Platform**: Supports all platforms with standardized field mapping
- **Simplified Output**: Removed JSON complexity, returns clean text format

### 2. Stream Processor Integration

**File**: `app/src/services/llm/core/stream_processor.py`

Key changes made:

```python
# Added tracking flag
preview_tool_executed = False

# Detection and streaming
if isinstance(result, dict) and result.get("type") == "stream_response":
    preview_tool_executed = True
    
    # Stream content directly to user
    yield "\n"
    async for chunk in result["stream_generator"]:
        yield chunk
    yield "\n"

# Prevent follow-up response
if not error and not preview_tool_executed:
    # Only generate follow-up if preview tool was NOT executed
    followup_response = self.llm_client.create_stream_completion(...)
```

### 3. Flow Prevention Logic

The system now prevents follow-up responses when:
- A tool returns `{"type": "stream_response"}`
- The preview tool specifically is executed
- The streaming content is successfully delivered

## User Experience Flow

### Before Integration
```
User: "プレビューを表示してください"
Assistant: "プレビューを生成します..."
[Tool executes]
Assistant: "プレビューが生成されました。以下をご確認ください: [content]"
Assistant: "他に何かお手伝いできることはありますか？" // Unwanted follow-up
```

### After Integration
```
User: "プレビューを表示してください"
Assistant: "プレビューを生成します..."
[Tool executes and streams content directly]
LINE
■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]
■ ターゲット: [商品またはオーディエンス, 10–25 chars]
■ 配信条件: [年齢、性別、興味、地域など, 50–100 chars]
■ 年齢層: [18–65 or specific range]
■ 性別: [全て / 男性 / 女性]
■ 地域: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]
[End - No follow-up response]
```

## Technical Architecture

### Streaming Pipeline

1. **User Request** → Chatbot Controller
2. **LLM Response** → Stream Processor
3. **Tool Detection** → Preview Tool Execution
4. **Streaming Response** → Direct to User (bypasses follow-up)
5. **Client Receives** → Real-time preview content

### Response Types

#### Regular Tool Response
```json
{
  "download_url": "/path/to/file.xlsx"
}
```
→ **Result**: Gets follow-up LLM response

#### Preview Tool Response
```json
{
  "type": "stream_response",
  "content": "LINE\n■ キャンペーン名: [...]",
  "platform": "LINE Ads",
  "extraction_status": "success",
  "stream_generator": AsyncGenerator
}
```
→ **Result**: Streams directly, no follow-up

## Benefits

### 1. Improved User Experience
- **Real-time feedback**: Users see content as it's generated
- **No redundant responses**: Clean, focused interaction
- **Immediate preview**: No waiting for complete response

### 2. System Efficiency
- **Reduced API calls**: No unnecessary follow-up LLM requests
- **Better resource usage**: Streaming reduces memory usage
- **Faster response**: Direct streaming without additional processing

### 3. Consistent Behavior
- **Predictable flow**: Preview always ends cleanly
- **Platform agnostic**: Works the same across all platforms
- **Standardized format**: Consistent user experience

## Configuration

### Tool Registration
```python
# In tool registry
tool_registry.register_tool(PreviewTool(prompt_service, llm_client))
```

### Stream Processing
```python
# Automatic detection in stream processor
if result.get("type") == "stream_response":
    # Handle streaming and prevent follow-up
```

### Client Integration
```javascript
// Client receives streaming events
eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    // Preview content streams in real-time
};
```

## Testing

### Test Coverage
- ✅ Preview tool streaming integration
- ✅ Follow-up response prevention
- ✅ Multi-platform support
- ✅ Error handling
- ✅ Regular tool compatibility

### Test Files
- `test_no_followup_response.py` - Verifies follow-up prevention
- `test_stream_integration.py` - Tests streaming integration
- `streaming_integration_example.py` - Demonstrates flow

## Future Enhancements

### Potential Improvements
1. **Configurable streaming delay** for better UX
2. **Progress indicators** during content generation
3. **Partial content validation** during streaming
4. **Enhanced error recovery** for streaming failures
5. **Metrics collection** for streaming performance

### Extensibility
The streaming response pattern can be extended to other tools that need:
- Real-time content delivery
- Prevention of follow-up responses
- Direct user interaction

## Conclusion

The preview tool streaming integration successfully provides:
- ✅ Real-time streaming of preview content
- ✅ Prevention of unwanted follow-up responses
- ✅ Multi-platform support with standardized format
- ✅ Seamless integration with existing system
- ✅ Improved user experience and system efficiency

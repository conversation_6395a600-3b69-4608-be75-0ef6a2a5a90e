"""
Error handling utilities for LLM operations.

This module provides centralized error handling and user-friendly error responses.
"""

from typing import AsyncGenerator

from app.src.services.llm.core.llm_client import LLMClient


class ErrorHandler:
    """
    Handles error responses and provides user-friendly error messages.
    """

    def __init__(self, llm_client: LLMClient):
        """
        Initialize the error handler.

        Args:
            llm_client: LLM client for generating error responses
        """
        self.llm_client = llm_client

    async def stream_error_response(
        self,
        error_text: str,
        temperature: float = 0,
        max_tokens: int = 4096,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming error response using GPT.

        Args:
            error_text: The error message to explain
            temperature: Temperature for response generation
            max_tokens: Maximum tokens in response

        Yields:
            Streaming error response content
        """
        prompt = [
            {
                "role": "system",
                "content": (
                    "あなたはプロフェッショナルな日本人の広告コンサルタントです。"
                    "丁寧かつ親切な口調で、ユーザーにエラーや不足情報について優しく説明してください。"
                ),
            },
            {
                "role": "user",
                "content": error_text.strip(),
            },
        ]

        response = self.llm_client.create_stream_completion(
            messages=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
        )

        async for chunk in response:
            if chunk.choices and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

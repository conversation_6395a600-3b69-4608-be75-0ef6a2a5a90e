"""
Stream processing logic for handling OpenAI streaming responses and tool calls.

This module handles the complex logic of processing streaming responses,
managing tool calls, and coordinating between different components.
"""

import json
from pathlib import Path
from typing import Any, AsyncGenerator, Callable, Dict, List, Optional

from app.src.services.llm.core.llm_client import LL<PERSON><PERSON>
from app.src.services.llm.core.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from app.src.services.llm.tools.tool_registry import ToolRegistry
from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils import PromptGenerator


class StreamProcessor:
    """
    Processes streaming responses from OpenAI API and handles tool calls.
    """
    
    def __init__(
        self,
        llm_client: LLMClient,
        tool_registry: ToolRegistry,
        error_handler: ErrorHandler,
    ):
        """
        Initialize the stream processor.
        
        Args:
            llm_client: LLM client for API calls
            tool_registry: Registry of available tools
            error_handler: Error handler for error responses
        """
        self.llm_client = llm_client
        self.tool_registry = tool_registry
        self.error_handler = error_handler
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    async def process_stream(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Process a streaming response with tool call handling.

        Args:
            messages: List of message dictionaries
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter
            on_tool_call: Optional callback for tool call notifications

        Yields:
            Streaming response content
        """
        tools = self.tool_registry.get_tools_config()

        response = self.llm_client.create_stream_completion(
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            tools=tools,
            tool_choice="auto",
        )

        tool_calls = []
        streaming_content = ""
        error = False  # Use 'error' to match original logic

        # Process initial streaming response
        async for chunk in response:
            choice = chunk.choices[0]
            delta = choice.delta

            if delta.content:
                streaming_content += delta.content
                yield delta.content

            if delta.tool_calls:
                self._process_tool_call_chunks(delta.tool_calls, tool_calls)

            if choice.finish_reason in {"stop", "tool_calls"}:
                break

        # Handle tool calls if any
        if tool_calls:
            messages.append({"role": "assistant", "tool_calls": tool_calls})

            for tool_call in tool_calls:
                tool_name = tool_call["function"]["name"]

                if on_tool_call:
                    await on_tool_call(tool_name)

                try:
                    # Execute tool with conversation context
                    result = await self._execute_tool_call(tool_call, messages)

                    # Check if this is a streaming response from preview tool
                    if isinstance(result, dict) and result.get("type") == "stream_response":
                        # Handle streaming tool response
                        yield "\n"  # Add newline before streaming content

                        # Stream the content directly to user
                        if "stream_generator" in result:
                            async for chunk in result["stream_generator"]:
                                yield chunk
                        else:
                            # Fallback to static content if no generator
                            yield result.get("content", "")

                        yield "\n"  # Add newline after streaming content

                        # Add simplified result to messages for context
                        simplified_result = {
                            "type": "stream_response",
                            "platform": result.get("platform"),
                            "status": result.get("extraction_status", "success")
                        }
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": tool_name,
                            "content": json.dumps(simplified_result, ensure_ascii=False),
                        })
                    else:
                        # Handle regular tool response
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": tool_name,
                            "content": json.dumps(result, ensure_ascii=False),
                        })
                except Exception as e:
                    error = True  # Set error flag to match original logic
                    error_message = f"❌ {tool_name}の実行中にエラーが発生しました: {str(e)}"
                    async for chunk in self.error_handler.stream_error_response(
                        error_message, temperature, max_tokens
                    ):
                        yield chunk
                    # Don't return here - continue processing other tool calls

            # Generate follow-up response if no errors (matching original logic)
            if not error:
                followup_response = self.llm_client.create_stream_completion(
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                async for chunk in followup_response:
                    if chunk.choices and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
    
    def _process_tool_call_chunks(
        self, 
        tool_call_chunks: List[Any], 
        tool_calls: List[Dict[str, Any]]
    ) -> None:
        """
        Process tool call chunks from streaming response.
        
        Args:
            tool_call_chunks: Tool call chunks from delta
            tool_calls: List to accumulate tool calls
        """
        for tcchunk in tool_call_chunks:
            index = tcchunk.index
            while len(tool_calls) <= index:
                tool_calls.append({
                    "id": "",
                    "type": "function",
                    "function": {"name": "", "arguments": ""},
                })
            
            tool_call = tool_calls[index]
            tool_call["id"] += tcchunk.id or ""
            tool_call["function"]["name"] += tcchunk.function.name or ""
            tool_call["function"]["arguments"] += tcchunk.function.arguments or ""
    
    async def _execute_tool_call(
        self,
        tool_call: Dict[str, Any],
        messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Execute a tool call and return the result.

        Args:
            tool_call: Tool call dictionary
            messages: Conversation messages for context

        Returns:
            Tool execution result

        Raises:
            Exception: If tool execution fails
        """
        tool_name = tool_call["function"]["name"]
        args = json.loads(tool_call["function"]["arguments"])

        # Add conversation context for tools that need it
        conversation_context = {
            "messages": messages,
            "tool_call_id": tool_call["id"]
        }
        args["conversation_context"] = conversation_context

        tool = self.tool_registry.get_tool(tool_name)
        return await tool.execute(**args)

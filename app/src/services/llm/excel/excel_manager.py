"""
Excel manager for coordinating Excel file generation.

This module manages the creation of Excel files for different advertising platforms.
"""

import json
import os
import re
from pathlib import Path
from tempfile import gettempdir
from typing import Any, Dict
from uuid import uuid4

import aiofiles

from .formatters.meta_formatter import MetaFormatter
from .formatters.line_formatter import Line<PERSON>ormatter
from .formatters.youtube_formatter import YouTubeFormatter
from .formatters.google_search_formatter import GoogleSearchFormatter
from .formatters.google_display_formatter import GoogleDisplayFormatter
from .formatters.google_demand_gen_formatter import GoogleDemandGenFormatter
from .formatters.pmax_formatter import PM<PERSON>Formatter
from .formatters.ad_extensions_formatter import AdExtensionsFormatter


class ExcelManager:
    """
    Manages Excel file generation for different advertising platforms.
    """
    
    def __init__(self, outputs_dir: Path):
        """
        Initialize the Excel manager.
        
        Args:
            outputs_dir: Directory containing output format files
        """
        self.outputs_dir = outputs_dir
        self.formatters = {
            "Meta (Instagram/Facebook)": <PERSON>a<PERSON><PERSON><PERSON><PERSON>(),
            "LINE Ads": <PERSON><PERSON><PERSON><PERSON>er(),
            "YouTube Ads": <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            "Google Search Ads": GoogleSearchFormatter(),
            "Google Display Ads": GoogleDisplayFormatter(),
            "Google Demand Gen Ads": GoogleDemandGenFormatter(),
            "P-Max": PMaxFormatter(),
            "Ad Extensions": AdExtensionsFormatter(),
        }
        self.platform_mapping = {
            "LINE Ads": "line_ads_format.json",
            "Meta (Instagram/Facebook)": "meta_ads_format.json",
            "YouTube Ads": "youtube_ads_format.json",
            "Google Search Ads": "google_search_ads_format.json",
            "Google Display Ads": "google_display_ads_format.json",
            "Google Demand Gen Ads": "google_demand_gen_ads_format.json",
            "P-Max": "p_max_format.json",
            "Ad Extensions": "ad_extensions_format.json"
        }
    
    async def create_excel_file(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Create an Excel file for the specified platform.

        Args:
            platform: Advertising platform name
            campaign_data: Campaign data dictionary

        Returns:
            Download URL for the created file

        Raises:
            ValueError: If platform is not supported
            FileNotFoundError: If format file is not found
            PermissionError: If file creation fails due to permissions
            Exception: For other file creation errors
        """
        if platform not in self.formatters:
            supported_platforms = ", ".join(self.formatters.keys())
            raise ValueError(
                f"指定されたプラットフォーム「{platform}」は対応していません。"
                f"以下の中から選択してください：{supported_platforms}"
            )

        try:
            # Load output format
            format_file = self.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self._load_output_format(format_file)

            # Generate descriptive filename based on product and platform
            filename = self._generate_filename(platform, campaign_data)
            file_path = os.path.join(gettempdir(), filename)

            # Ensure temp directory exists and is writable
            temp_dir = gettempdir()
            if not os.path.exists(temp_dir):
                raise PermissionError(f"Temporary directory does not exist: {temp_dir}")
            if not os.access(temp_dir, os.W_OK):
                raise PermissionError(f"Cannot write to temporary directory: {temp_dir}")

            formatter = self.formatters[platform]
            await formatter.write_excel_file(campaign_data, output_format, file_path)

            # Verify file was created successfully
            if not os.path.exists(file_path):
                raise Exception("Excel file was not created successfully")

            # Verify file has content
            if os.path.getsize(file_path) == 0:
                raise Exception("Excel file was created but is empty")

            download_url = f"/be/chat/download-excel?filename={filename}"
            return download_url

        except json.JSONDecodeError as e:
            raise Exception(f"フォーマットファイルの解析に失敗しました: {str(e)}")
        except FileNotFoundError as e:
            if "format" in str(e).lower():
                raise FileNotFoundError(f"プラットフォーム「{platform}」のフォーマットファイルが見つかりません")
            else:
                raise FileNotFoundError("必要なファイルが見つかりません")
        except PermissionError as e:
            raise PermissionError("ファイル作成の権限がありません")
        except Exception as e:
            # Re-raise specific exceptions
            if isinstance(e, (ValueError, FileNotFoundError, PermissionError)):
                raise e
            else:
                raise Exception(f"Excelファイル作成中にエラーが発生しました: {str(e)}")
    
    async def _load_output_format(self, format_file: str) -> Dict[str, Any]:
        """
        Load output format from JSON file.

        Args:
            format_file: Format file name

        Returns:
            Output format dictionary

        Raises:
            FileNotFoundError: If format file is not found
            json.JSONDecodeError: If JSON parsing fails
        """
        file_path = self.outputs_dir / format_file

        try:
            if not file_path.exists():
                raise FileNotFoundError(f"Format file not found: {format_file}")

            async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
                content = await f.read()

            if not content.strip():
                raise json.JSONDecodeError("Format file is empty", format_file, 0)

            return json.loads(content)

        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"Invalid JSON in format file {format_file}: {str(e)}", format_file, e.pos)
        except Exception as e:
            if isinstance(e, FileNotFoundError):
                raise e
            else:
                raise Exception(f"Error loading format file {format_file}: {str(e)}")

    def _generate_filename(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Generate a descriptive filename based on product and platform information.

        Args:
            platform: Advertising platform name
            campaign_data: Campaign data dictionary

        Returns:
            Safe filename for the Excel file
        """
        # Extract product/company information from campaign data
        data = campaign_data.get("data", {})

        # Try to get product/company name from various fields
        product_name = self._extract_product_name(data)

        # Get platform short name
        platform_short = self._get_platform_short_name(platform)

        # Create base filename
        if product_name:
            base_name = f"{product_name}_{platform_short}"
        else:
            base_name = f"Campaign_{platform_short}"

        # Make filename safe for file systems
        safe_name = self._make_filename_safe(base_name)

        # Add unique identifier and extension
        unique_id = uuid4().hex[:8]  # Use shorter UUID for readability
        filename = f"{safe_name}_{unique_id}.xlsx"

        return filename

    def _extract_product_name(self, data: Dict[str, Any]) -> str:
        """
        Extract product or company name from campaign data.

        Args:
            data: Campaign data dictionary

        Returns:
            Product/company name or empty string if not found
        """
        # Priority order for extracting product information
        name_fields = [
            "会社名",           # Company name (P-Max)
            "キャンペーン名",    # Campaign name (most platforms)
            "キャンペーン",      # Campaign (LINE Ads, Google Search)
            "商品名",           # Product name (if available)
            "ブランド名",        # Brand name (if available)
        ]

        for field in name_fields:
            value = data.get(field)
            if value:
                # Handle different value types
                if isinstance(value, str) and value.strip():
                    # Extract meaningful part from campaign name
                    return self._clean_product_name(value.strip())
                elif isinstance(value, list) and value:
                    # Take first item if it's a list
                    first_item = value[0]
                    if isinstance(first_item, str) and first_item.strip():
                        return self._clean_product_name(first_item.strip())

        return ""

    def _clean_product_name(self, name: str) -> str:
        """
        Clean and extract meaningful product name from campaign name.

        Args:
            name: Raw name string

        Returns:
            Cleaned product name
        """
        # Remove common campaign suffixes/prefixes
        cleaned = name

        # Remove common Japanese campaign terms
        campaign_terms = [
            "キャンペーン", "広告", "プロモーション", "宣伝",
            "Campaign", "Ad", "Ads", "Promotion"
        ]

        for term in campaign_terms:
            cleaned = re.sub(rf"\s*{re.escape(term)}\s*", "", cleaned, flags=re.IGNORECASE)

        # Take first meaningful part (before common separators)
        separators = ["_", "-", "・", "｜", "|", "×", "x"]
        for sep in separators:
            if sep in cleaned:
                parts = cleaned.split(sep)
                if parts[0].strip():
                    cleaned = parts[0].strip()
                    break

        # Limit length for filename
        if len(cleaned) > 20:
            cleaned = cleaned[:20]

        return cleaned.strip()

    def _get_platform_short_name(self, platform: str) -> str:
        """
        Get short name for platform to use in filename.

        Args:
            platform: Full platform name

        Returns:
            Short platform name
        """
        platform_short_names = {
            "LINE Ads": "LINE",
            "Meta (Instagram/Facebook)": "Meta",
            "YouTube Ads": "YouTube",
            "Google Search Ads": "GoogleSearch",
            "Google Display Ads": "GoogleDisplay",
            "Google Demand Gen Ads": "GoogleDemandGen",
            "P-Max": "PMax",
            "Ad Extensions": "AdExtensions"
        }

        return platform_short_names.get(platform, platform.replace(" ", ""))

    def _make_filename_safe(self, filename: str) -> str:
        """
        Make filename safe for file systems by removing/replacing invalid characters.

        Args:
            filename: Original filename

        Returns:
            Safe filename
        """
        # Replace invalid characters with underscores
        # Invalid characters for most file systems: < > : " | ? * \ /
        invalid_chars = r'[<>:"|?*\\/]'
        safe_name = re.sub(invalid_chars, "_", filename)

        # Replace multiple underscores with single underscore
        safe_name = re.sub(r"_+", "_", safe_name)

        # Remove leading/trailing underscores and spaces
        safe_name = safe_name.strip("_ ")

        # Ensure filename is not empty
        if not safe_name:
            safe_name = "Campaign"

        return safe_name

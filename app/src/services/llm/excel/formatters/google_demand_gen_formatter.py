"""
Google Demand Gen Ads Excel formatter.

This module handles Excel file generation for Google Demand Gen advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleDemandGenFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Demand Gen advertising campaigns.
    """

    async def write_excel_file(
        self, campaign_data: Dict[str, Any], output_format: Dict[str, Any], file_path: str
    ) -> None:
        """
        Write Excel file for Google Demand Gen advertising campaigns.

        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("Googleデマンドジェネレーション")

        # Headers
        headers = ["媒体", "キャンペーン名", "広告グループ名", "配信条件", "デバイス", "性別", "年齢", "エリア"]

        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        data = campaign_data.get("data", {})

        # Non-list fields
        non_list_fields = ["媒体", "キャンペーン名", "デバイス", "性別", "年齢", "エリア"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)

        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                values = self.ensure_list(values, max_items=2)
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Link destination
        set_cell(ws, 4, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=8)

        # Google URL
        link_url = self.get_field_value(data.get("Google　URL", "?utm_source=google&utm_medium=display"))
        set_cell(ws, 5, 1, "Google　URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 5, 2, link_url, merge_end_col=8)

        # Ad content headers
        set_cell(ws, 7, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 7, 2, "配信内容", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 7, 3, "広告文", merge_end_col=7, fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 7, 8, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)

        # Ad type
        ad_type = self.get_field_value(data.get("広告種類", "レスポンシブ ディスプレイ広告"))

        # Content configurations
        content_configs = [
            {"key": "主体者表記", "label": "主体者表記（最大25文字）", "is_list": False},
            {"key": "広告見出し 短縮", "label_base": "広告見出し 短縮（最大40文字）", "is_list": True},
            {"key": "説明文", "label_base": "説明文（最大90文字）", "is_list": True},
        ]

        row = 8
        total_rows = 8  # Track total rows for merging 広告種類 (matching original logic)

        for config in content_configs:
            key = config["key"]
            is_list = config["is_list"]

            values = self.get_field_value(data.get(key, [] if is_list else ""))
            if not is_list:
                values = [values]

            for idx, value in enumerate(values):
                if "label_base" in config:
                    label = config["label_base"] if idx == 0 else f"{config['label_base']}【任意】"
                else:
                    label = config["label"]

                set_cell(ws, row, 2, label)
                set_cell(ws, row, 3, value, merge_end_col=7)
                set_cell(ws, row, 8, len(value) if value else 0)
                row += 1
                total_rows += 1

        # Set 広告種類 after calculating total rows (matching original logic)
        set_cell(ws, 8, 1, ad_type, merge_end_row=total_rows - 1)

        self.save_workbook(wb, file_path)

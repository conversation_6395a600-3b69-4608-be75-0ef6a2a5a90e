"""
P-Max Ads Excel formatter.

This module handles Excel file generation for P-Max advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class PMaxFormatter(BaseExcelWriter):
    """
    Excel formatter for P-Max advertising campaigns.
    """

    async def write_excel_file(
        self, campaign_data: Dict[str, Any], output_format: Dict[str, Any], file_path: str
    ) -> None:
        """
        Write Excel file for P-Max advertising campaigns.

        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("P-Max広告フォーマット")

        # Headers
        headers = [
            "媒体",
            "キャンペーン名",
            "アセットグループ名",
            "オーディエンスシグナル",
            "デバイス",
            "性別",
            "年齢",
            "エリア",
        ]

        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        data = campaign_data.get("data", {})

        # All fields are non-list for P-Max
        for idx, header in enumerate(headers, start=1):
            ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
            value = self.get_field_value(data.get(header, "P-Max" if header == "媒体" else ""))
            set_cell(ws, 2, idx, value)

        # Link destination
        set_cell(ws, 4, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=8)

        # Ad content headers
        set_cell(ws, 7, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 7, 2, "配信内容", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 7, 3, "広告文", fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=7)
        set_cell(ws, 7, 8, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)

        # Company name
        company_name = self.get_field_value(data.get("会社名", ""))
        set_cell(ws, 8, 2, "会社名（最大12文字）")
        set_cell(ws, 8, 3, company_name, merge_end_col=7)
        set_cell(ws, 8, 8, str(len(company_name)))

        current_row = 9

        # Short headlines (up to 15)
        short_headlines = self.get_field_value(data.get("広告見出し", []))
        short_headlines = self.ensure_list(short_headlines, max_items=15)
        for i, headline in enumerate(short_headlines):
            label = "広告見出し （最大15文字）" + ("【任意】" if i >= 1 else "")
            set_cell(ws, current_row, 2, label)
            set_cell(ws, current_row, 3, headline, merge_end_col=7)
            set_cell(ws, current_row, 8, str(len(headline)))
            current_row += 1

        # Long headlines (up to 5)
        long_headlines = self.get_field_value(data.get("長い広告見出し", []))
        long_headlines = self.ensure_list(long_headlines, max_items=5)
        for i, headline in enumerate(long_headlines):
            label = "長い広告見出し （最大45文字）" + ("【任意】" if i >= 1 else "")
            set_cell(ws, current_row, 2, label)
            set_cell(ws, current_row, 3, headline, merge_end_col=7)
            set_cell(ws, current_row, 8, str(len(headline)))
            current_row += 1

        # Descriptions
        descriptions = self.get_field_value(data.get("説明文", []))
        descriptions = self.ensure_list(descriptions, max_items=5)

        # First description (short)
        if descriptions:
            short_desc = descriptions[0]
            set_cell(ws, current_row, 2, "説明文（最大30文字）")
            set_cell(ws, current_row, 3, short_desc, merge_end_col=7)
            set_cell(ws, current_row, 8, str(len(short_desc)))
            current_row += 1

            # Remaining descriptions (long)
            for desc in descriptions[1:]:
                set_cell(ws, current_row, 2, "説明文（最大45文字）【任意】")
                set_cell(ws, current_row, 3, desc, merge_end_col=7)
                set_cell(ws, current_row, 8, str(len(desc)))
                current_row += 1

        # Set P-Max label with proper merging
        set_cell(ws, 8, 1, "P-Max", merge_end_row=current_row - 1)

        self.save_workbook(wb, file_path)

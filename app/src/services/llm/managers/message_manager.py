"""Message management functionality for LLM service."""

from typing import Any, List

from app.src.schemas.chat_sessions import ChatHistoryItem


class MessageManager:
    """Manages message preparation and chat history utilities."""

    def __init__(self):
        """Initialize the message manager."""
        pass

    def prepare_messages(
        self,
        system_prompt: str,
        chat_history: List[ChatHistoryItem],
        current_request: Any,
    ) -> List[dict]:
        """
        Prepare messages for LLM API call.

        Args:
            system_prompt: System prompt to use
            chat_history: List of previous chat messages
            current_request: Current user request object

        Returns:
            List of formatted messages for the LLM API
        """
        messages = [{"role": "system", "content": system_prompt}]

        # Add chat history
        for item in chat_history:
            messages.append({"role": item.role, "content": item.content})

        # Add current user message
        messages.append({"role": "user", "content": current_request.question})

        return messages

    @staticmethod
    def should_update_summary(chat_history: List[ChatHistoryItem]) -> bool:
        """
        Determine if chat history summary should be updated.

        Args:
            chat_history: List of chat history items

        Returns:
            True if summary should be updated, False otherwise
        """
        return len(chat_history) == 0 or len(chat_history) % 20 == 0

    @staticmethod
    def extract_questions(chat_history: List[ChatHistoryItem], current_question: str) -> List[str]:
        """
        Extract questions from chat history for analysis.

        Args:
            chat_history: List of chat history items
            current_question: Current user question

        Returns:
            List of questions including the current one
        """
        if not chat_history:
            return [current_question]

        # Extract questions (assuming they are at even indices in the conversation)
        questions = [msg.content for i, msg in enumerate(chat_history) if i % 2 == 0]
        questions.append(current_question)

        return questions

    @staticmethod
    def count_tokens_in_history(chat_history: List[ChatHistoryItem]) -> int:
        """
        Count approximate tokens in chat history.

        Args:
            chat_history: List of chat history items

        Returns:
            Approximate token count
        """
        total_content = " ".join([item.content for item in chat_history])
        # Rough approximation: 1 token ≈ 4 characters
        return len(total_content) // 4

    def get_recent_context(
        self,
        chat_history: List[ChatHistoryItem],
        max_messages: int = 10,
    ) -> List[ChatHistoryItem]:
        """
        Get recent context from chat history.

        Args:
            chat_history: Full chat history
            max_messages: Maximum number of recent messages to return

        Returns:
            List of recent chat history items
        """
        if len(chat_history) <= max_messages:
            return chat_history

        return chat_history[-max_messages:]

    def format_history_for_summary(self, chat_history: List[ChatHistoryItem]) -> str:
        """
        Format chat history for summarization.

        Args:
            chat_history: List of chat history items

        Returns:
            Formatted string representation of the chat history
        """
        formatted_messages = []

        for item in chat_history:
            role_prefix = "User" if item.role == "user" else "Assistant"
            formatted_messages.append(f"{role_prefix}: {item.content}")

        return "\n\n".join(formatted_messages)

"""Tool management functionality for LLM service."""

from typing import List, Optional

from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

from ..core.llm_client import LLMClient
from ..tools.csv_tool import CSVTool
from ..tools.excel_tool import ExcelTool
from ..tools.preview_tool import PreviewTool
from ..tools.tool_registry import ToolRegistry
from ..tools.web_search_tool import WebSearchTool


class ToolManager:
    """Manages tool registration and configuration for LLM operations."""

    def __init__(
        self,
        search_service: Optional[SearchService] = None,
        prompt_service: Optional[PromptService] = None,
        llm_client: Optional[LLMClient] = None,
    ):
        """
        Initialize the tool manager.

        Args:
            search_service: Search service for web search functionality
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for tool operations
        """
        self.search_service = search_service
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.tool_registry = ToolRegistry()
        self._register_tools()

    def _register_tools(self) -> None:
        """Register all available tools in the tool registry."""
        # Register web search tool if search service is available
        if self.search_service:
            self.tool_registry.register_tool(WebSearchTool(self.search_service))

        # Register CSV tool (always available)
        self.tool_registry.register_tool(CSVTool())

        # Register Excel tool if prompt service and LLM client are available
        if self.prompt_service and self.llm_client:
            self.tool_registry.register_tool(ExcelTool(self.prompt_service, self.llm_client))

        # Register Preview tool if prompt service and LLM client are available
        if self.prompt_service and self.llm_client:
            self.tool_registry.register_tool(PreviewTool(self.prompt_service, self.llm_client))

    def get_tool_registry(self) -> ToolRegistry:
        """
        Get the configured tool registry.

        Returns:
            Configured tool registry instance
        """
        return self.tool_registry

    def get_available_tools(self) -> List[str]:
        """
        Get list of available tool names.

        Returns:
            List of available tool names
        """
        return self.tool_registry.list_tools()

    def get_tool_configs(self) -> List[dict]:
        """
        Get configuration for all available tools.

        Returns:
            List of tool configurations
        """
        return self.tool_registry.get_tools_config()

    def register_additional_tool(self, tool) -> None:
        """
        Register an additional tool at runtime.

        Args:
            tool: Tool instance to register
        """
        self.tool_registry.register_tool(tool)

    def unregister_tool(self, tool_name: str) -> bool:
        """
        Unregister a tool by name.

        Args:
            tool_name: Name of the tool to unregister

        Returns:
            True if tool was found and unregistered, False otherwise
        """
        # Note: This would require implementing unregister functionality in ToolRegistry
        # For now, we'll return False as this functionality doesn't exist yet
        return False

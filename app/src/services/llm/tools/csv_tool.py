"""
CSV file creation tool for LLM function calling.

This tool creates downloadable CSV files from provided content.
"""

import csv
import os
from tempfile import gettempdir
from typing import Any, Dict
from uuid import uuid4

from .base_tool import BaseTool


class CSVTool(BaseTool):
    """
    Tool for creating CSV files.

    This tool creates downloadable CSV files from text content.
    """

    @property
    def name(self) -> str:
        """Return the tool name."""
        return "create_csv_file"

    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Creates a downloadable CSV file from the given content."

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": ("The content to store in CSV, plain text, line by line or comma separated."),
                }
            },
            "required": ["content"],
        }

    async def execute(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Execute CSV file creation.

        Args:
            content: CSV content as string
            **kwargs: Additional parameters

        Returns:
            Dictionary with download URL
        """
        filename = f"gen_csv_{uuid4().hex}.csv"
        file_path = os.path.join(gettempdir(), filename)

        with open(file_path, "w", encoding="utf-8", newline="") as f:
            writer = csv.writer(f)
            for line in content.strip().split("\n"):
                writer.writerow([cell.strip() for cell in line.split(",")])

        download_url = f"/be/chat/download-csv?filename={filename}"
        return {"download_url": download_url}

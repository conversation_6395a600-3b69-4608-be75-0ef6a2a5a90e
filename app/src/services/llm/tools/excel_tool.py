"""
Excel file creation tool for LLM function calling.

This tool creates downloadable Excel files for advertising campaigns.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService

from .base_tool import BaseTool


class ExcelTool(BaseTool):
    """
    Tool for creating Excel files for advertising campaigns.

    This tool handles the complete workflow of Excel file generation
    including data extraction, formatting, and file creation.
    """

    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the Excel tool.

        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()

    @property
    def name(self) -> str:
        """Return the tool name."""
        return "create_excel_file"

    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Creates a downloadable Excel file for an advertising campaign based on the specified platform."

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max",
                        "Ad Extensions",
                    ],
                }
            },
            "required": ["platform"],
        }

    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute Excel file creation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with download URL
        """
        # Check if platform is supported (matching original logic)
        if platform not in self.excel_manager.platform_mapping:
            supported_platforms = "YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads・Ad Extensions"
            raise Exception(
                f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：{supported_platforms}"
            )

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get("conversation_context", {})
            messages = conversation_context.get("messages", [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。Excelファイルを作成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「Excelファイルを再作成してください」とお伝えください。"
                )

            # Extract campaign data from conversation
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Check for extraction errors (matching original logic)
            if "error" in campaign_data:
                raise Exception("❌ プラットフォームが不明です。対応している広告プラットフォームを指定してください。")
            

            # Create Excel file
            download_url = await self.excel_manager.create_excel_file(platform, campaign_data)

            # Format the extracted data into preview content (same as preview tool)
            formatted_preview = await self._format_preview_content(platform, campaign_data)

            # Store the information for followup response
            excel_info = {
                "platform": platform,
                "content": formatted_preview,
                "download_url": download_url,
                "extraction_status": "success",
                "data_summary": self._create_data_summary(campaign_data)
            }

            # Return with followup_response including download link
            return {
                "type": "followup_response",
                "followup_response": self._create_excel_followup_response(excel_info),
                "platform": platform,
                "extraction_status": "success",
                "download_url": download_url,
                "preview_content": formatted_preview
            }

        except json.JSONDecodeError:
            raise Exception(
                "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                "広告コンテンツを再作成してから、もう一度Excelファイルの作成をお試しください。"
            )
        except FileNotFoundError:
            raise Exception(
                "❌ 必要なファイルが見つかりません。システム設定に問題がある可能性があります。\n"
                "しばらく時間をおいてから再度お試しください。"
            )
        except PermissionError:
            raise Exception(
                "❌ ファイル作成の権限がありません。システムの一時的な問題の可能性があります。\n"
                "しばらく時間をおいてから再度お試しください。"
            )
        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str:
                raise e
            elif "JSON" in error_str or "解析" in error_str:
                raise Exception(
                    "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                    "広告コンテンツを再作成してから、もう一度Excelファイルの作成をお試しください。"
                )
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ Excelファイル作成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。既に作成済みの場合は、「Excelファイルを再作成してください」とお伝えください。"
                )

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform, fields=schema_fields
            )

            # Get system prompt and enhance it (matching original logic exactly)
            # system_prompt = await self.prompt_service._get_prompts()
            enhanced_prompt = (
                f"\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "For Google Search Ads or Google Display Ads, confirm whether the platform is Google or Yahoo based on user-provided data. If unclear, list 'platform_confirmation' in missing_fields. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                '{\n  "data": { ... },\n  "missing_fields": [ ... ]\n}\n\n'
                "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
            )

            print(f"\n📥 Generated system_prompt:\n{enhanced_prompt}\n")

            # Prepare messages for extraction (matching original logic)
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history excluding system prompt and last message (matching original: *messages[1:-1])
            if messages and len(messages) > 2:
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages, max_tokens=4096, temperature=0
            )

            # Parse the response (matching original logic)
            campaign_data = json.loads(response.choices[0].message.content)
            return campaign_data

        except json.JSONDecodeError:
            # Re-raise to match original error handling
            raise json.JSONDecodeError("JSON parsing failed", "", 0)
        except Exception as e:
            # Re-raise to match original error handling
            raise e

    def _get_default_campaign_data(self, platform: str) -> Dict[str, Any]:
        """
        Get default campaign data when extraction fails.

        Args:
            platform: Advertising platform

        Returns:
            Default campaign data structure
        """
        return {
            "data": {
                "キャンペーン名": f"{platform}キャンペーン",
                "広告グループ名": ["グループ1", "グループ2"],
                "配信条件": ["条件1", "条件2"],
                "性別": "すべて",
                "年齢": "18-65",
                "エリア": "日本全国",
                "見出し": [f"{platform}の魅力的な見出し", "効果的な広告文"],
                "説明文": [f"{platform}向けの説明文", "詳細な商品説明"],
            },
            "missing_fields": [],
        }

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し",
            "ヘッドライン",
            "headline",
            "タイトル",
            "説明文",
            "description",
            "広告文",
            "コピー",
            "キャンペーン",
            "campaign",
            "広告グループ",
            "ターゲット",
            "target",
            "配信",
            "予算",
            "キーワード",
            "keyword",
            "CTA",
            "行動喚起",
            "商品",
            "サービス",
            "ブランド",
            "企業",
            "広告",
            "ad",
            "プロモーション",
            "宣伝",
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

    def _has_sufficient_content(self, campaign_data: Dict[str, Any]) -> bool:
        """
        Check if the extracted campaign data has sufficient content for Excel creation.

        Args:
            campaign_data: Extracted campaign data

        Returns:
            True if sufficient content is available, False otherwise
        """
        if not campaign_data or "data" not in campaign_data:
            return False

        data = campaign_data["data"]
        if not isinstance(data, dict):
            return False

        # Check for essential fields that should have meaningful content
        essential_fields = ["見出し", "説明文", "キャンペーン名"]
        content_count = 0

        for field in essential_fields:
            field_value = data.get(field)
            if field_value:
                if isinstance(field_value, list) and len(field_value) > 0:
                    # Check if list contains non-empty strings
                    if any(str(item).strip() for item in field_value):
                        content_count += 1
                elif isinstance(field_value, str) and field_value.strip():
                    content_count += 1

        # Require at least 2 out of 3 essential fields to have content
        return content_count >= 2

    async def _format_preview_content(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Format extracted campaign data into preview content.

        Args:
            platform: Advertising platform
            campaign_data: Extracted campaign data from LLM

        Returns:
            Formatted preview content string
        """
        if not campaign_data or "data" not in campaign_data:
            return "❌ 抽出されたデータが不完全です。"

        data = campaign_data["data"]

        # Load the platform format to get field descriptions
        format_file = self.excel_manager.platform_mapping.get(platform)
        output_format = await self.excel_manager._load_output_format(format_file)
        schema_fields = output_format.get("fields", {})

        # Get platform display name
        platform_display_name = self._get_platform_display_name(platform)

        # Build formatted preview using extracted data
        preview_lines = [platform_display_name]

        # Create standardized field mapping with actual values
        standardized_fields = self._create_standardized_fields_with_data(schema_fields, data)

        for field_name, field_value in standardized_fields.items():
            preview_lines.append(f"■ {field_name}: {field_value}")

        return "\n".join(preview_lines)

    def _get_platform_display_name(self, platform: str) -> str:
        """Get display name for platform."""
        platform_names = {
            "youtube": "YouTube広告",
            "meta": "Meta広告",
            "google_search": "Google検索広告",
            "google_display": "Googleディスプレイ広告",
            "google_demand_gen": "Googleディマンドジェン広告",
            "pmax": "P-Max広告",
            "line_ads": "LINE Ads",
            "ad_extensions": "広告表示オプション"
        }
        return platform_names.get(platform, f"{platform}広告")

    def _create_data_summary(self, campaign_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of the extracted campaign data.

        Args:
            campaign_data: Extracted campaign data from LLM

        Returns:
            Summary information about the data
        """
        data = campaign_data.get("data", {})
        missing_fields = campaign_data.get("missing_fields", [])

        # Count different types of data
        total_fields = len(data)
        populated_fields = len([v for v in data.values() if v])

        # Analyze data complexity
        complex_fields = 0
        list_fields = 0
        for value in data.values():
            if isinstance(value, dict):
                complex_fields += 1
            elif isinstance(value, list):
                list_fields += 1

        return {
            "total_fields": total_fields,
            "populated_fields": populated_fields,
            "missing_fields": len(missing_fields),
            "complex_fields": complex_fields,
            "list_fields": list_fields
        }

    def _create_excel_followup_response(self, excel_info: Dict[str, Any]) -> str:
        """
        Create followup response message for Excel creation.

        Args:
            excel_info: Information about the created Excel file

        Returns:
            Followup response message
        """
        platform = excel_info["platform"]
        content = excel_info["content"]
        download_url = excel_info["download_url"]
        data_summary = excel_info["data_summary"]

        # Create the response message
        response_parts = []

        # Add platform-specific header
        response_parts.append(f"📊 **{platform} Excelファイル作成完了**")
        response_parts.append("")

        # Add download link
        response_parts.append("🔗 **ダウンロードリンク:**")
        response_parts.append(f"[Excelファイルをダウンロード]({download_url})")
        response_parts.append("")

        # Add the formatted preview content
        response_parts.append("📋 **作成されたコンテンツ:**")
        response_parts.append("```")
        response_parts.append(content)
        response_parts.append("```")
        response_parts.append("")

        # Add data summary
        response_parts.append("📊 **データ概要:**")
        response_parts.append(f"• 総フィールド数: {data_summary['total_fields']}")
        response_parts.append(f"• 入力済みフィールド: {data_summary['populated_fields']}")

        if data_summary["missing_fields"] > 0:
            response_parts.append(f"• 未入力フィールド: {data_summary['missing_fields']}")

        if data_summary["list_fields"] > 0:
            response_parts.append(f"• リスト形式フィールド: {data_summary['list_fields']}")

        if data_summary["complex_fields"] > 0:
            response_parts.append(f"• 複合フィールド: {data_summary['complex_fields']}")

        response_parts.append("")

        # Add next steps
        response_parts.append("✅ **次のステップ:**")
        response_parts.append("• Excelファイルをダウンロードして内容を確認してください")
        response_parts.append("• 必要に応じて手動で調整を行ってください")
        response_parts.append("• 他のプラットフォーム用のExcelファイルが必要な場合はお知らせください")

        return "\n".join(response_parts)

    def _create_standardized_fields_with_data(
        self, schema_fields: Dict[str, Any], data: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Create output fields with actual extracted data values for all platforms.

        This function recursively processes all nested structures until reaching
        actual string values, ensuring complete information extraction.

        Args:
            schema_fields: Platform-specific schema fields from JSON file
            data: Extracted campaign data

        Returns:
            Dictionary of field names and their formatted values
        """
        formatted_fields = {}

        # Process all fields from the schema
        for field_name, field_config in schema_fields.items():
            # Get the actual value from extracted data
            actual_value = data.get(field_name, "")

            # Recursively extract all nested content
            extracted_fields = self._recursive_field_extraction(field_name, actual_value, field_config)
            formatted_fields.update(extracted_fields)

        return formatted_fields

    def _recursive_field_extraction(self, field_name: str, value: Any, field_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Recursively extract all nested content until reaching actual string values.

        Args:
            field_name: Name of the current field
            value: The value to process (can be any type)
            field_config: Configuration for the field

        Returns:
            Dictionary of all extracted field-value pairs
        """
        extracted_fields = {}

        # Base case: if value is None, empty, or a simple type (str, int, float, bool)
        if value is None or value == "" or isinstance(value, (str, int, float, bool)):
            if value or value == 0 or value is False:  # Include 0 and False as valid values
                formatted_value = self._format_simple_value(value, field_config)
                extracted_fields[field_name] = formatted_value
            else:
                # Create placeholder for empty values
                placeholder = self._create_field_placeholder(
                    field_config, field_config.get("description", ""), field_config.get("options", [])
                )
                extracted_fields[field_name] = placeholder
            return extracted_fields

        # Recursive case: handle complex structures
        if isinstance(value, dict):
            # Process dictionary recursively
            for sub_key, sub_value in value.items():
                sub_field_name = f"{field_name} - {sub_key}"
                sub_extracted = self._recursive_field_extraction(sub_field_name, sub_value, {})
                extracted_fields.update(sub_extracted)

        elif isinstance(value, list):
            # Process list recursively - each item on separate line
            if not value:
                extracted_fields[field_name] = "[Empty list]"
            else:
                # Process each list item separately with individual character counts
                for i, item in enumerate(value):
                    item_field_name = f"{field_name} - Item {i + 1}"

                    if isinstance(item, (str, int, float, bool)):
                        # Simple item - format with individual character count
                        item_str = str(item)
                        if field_config.get("max_chars") or field_config.get("min_chars"):
                            char_count = len(item_str)
                            char_info = self._get_char_range_info(field_config, char_count)
                            formatted_item = f"{item_str} {char_info}"
                        else:
                            formatted_item = item_str
                        extracted_fields[item_field_name] = formatted_item
                    else:
                        # Complex item - process recursively
                        item_extracted = self._recursive_field_extraction(item_field_name, item, {})
                        extracted_fields.update(item_extracted)

        else:
            # Handle any other type as string
            formatted_value = self._format_simple_value(str(value), field_config)
            extracted_fields[field_name] = formatted_value

        return extracted_fields

    def _format_simple_value(self, value: Any, field_config: Dict[str, Any]) -> str:
        """
        Format a simple value with character count information.

        Args:
            value: The value to format
            field_config: Field configuration

        Returns:
            Formatted value string
        """
        value_str = str(value)

        # Add character count information if limits are specified
        if field_config.get("max_chars") or field_config.get("min_chars"):
            char_count = len(value_str)
            char_info = self._get_char_range_info(field_config, char_count)
            return f"{value_str} {char_info}"

        return value_str

    def _get_char_range_info(self, field_config: Dict[str, Any], char_count: int) -> str:
        """
        Get character range information for a field.

        Args:
            field_config: Field configuration
            char_count: Current character count

        Returns:
            Character range information string
        """
        max_chars = field_config.get("max_chars")
        min_chars = field_config.get("min_chars")

        if max_chars and min_chars:
            return f"({char_count}/{min_chars}-{max_chars} chars)"
        elif max_chars:
            return f"({char_count}/{max_chars} chars)"
        elif min_chars:
            return f"({char_count}/{min_chars}+ chars)"
        else:
            return f"({char_count} chars)"

    def _create_field_placeholder(self, field_config: Dict[str, Any], description: str, options: list) -> str:
        """Create appropriate placeholder for missing data."""
        # Use options if available
        if options:
            return f"[{' / '.join(options)}]"

        # Use description if available
        if description:
            char_range = self._get_char_range(field_config)
            if char_range:
                return f"[{description}, {char_range} chars]"
            else:
                return f"[{description}]"

        # Default placeholder
        char_range = self._get_char_range(field_config)
        if char_range:
            return f"[未設定, {char_range} chars]"
        else:
            return "[未設定]"

    def _get_char_range(self, field_config: Dict[str, Any]) -> str:
        """Get character range string for field configuration."""
        max_chars = field_config.get("max_chars")
        min_chars = field_config.get("min_chars")

        if max_chars and min_chars:
            return f"{min_chars}-{max_chars}"
        elif max_chars:
            return f"max {max_chars}"
        elif min_chars:
            return f"min {min_chars}"
        else:
            return ""

"""
Preview ad content tool for LLM function calling.

This tool generates a preview of extracted ad content for the LINE platform,
allowing users to review and refine content before exporting to Excel.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class PreviewTool(BaseTool):
    """
    Tool for previewing ad content extracted from conversation history.
    
    This tool handles data extraction and formatting for preview display,
    reusing the same logic as the Excel creation tool but outputting
    human-readable formatted content instead of creating files.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the preview tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "preview_ad_content"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Generates a preview of extracted ad content for advertising platforms, formatted in a human-readable structure based on conversation history."

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max",
                        "Ad Extensions"
                    ]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute ad content preview generation.

        Args:
            platform: Advertising platform name (must be "LINE Ads")
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with formatted preview content
        """
        # Check if platform is supported by excel manager
        if platform not in self.excel_manager.platform_mapping:
            supported_platforms = "YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads・Ad Extensions"
            raise Exception(f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：{supported_platforms}")

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get('conversation_context', {})
            messages = conversation_context.get('messages', [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。プレビューを生成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「プレビューを再生成してください」とお伝えください。"
                )

            # Extract campaign data from conversation
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Check for extraction errors
            if "error" in campaign_data:
                raise Exception("❌ データの抽出に失敗しました。広告コンテンツを確認してください。")

            # Format the extracted data for preview
            formatted_preview = await self._format_preview_content(platform, campaign_data)

            return {
                "preview_content": formatted_preview,
                "platform": platform,
                "extraction_status": "success"
            }

        except json.JSONDecodeError as e:
            raise Exception(
                "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                "広告コンテンツを再作成してから、もう一度プレビューの生成をお試しください。"
            )
        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str or "LINE Ads" in error_str:
                raise e
            elif "JSON" in error_str or "解析" in error_str:
                raise Exception(
                    "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                    "広告コンテンツを再作成してから、もう一度プレビューの生成をお試しください。"
                )
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ プレビュー生成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。"
                )

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.
        
        This method reuses the same extraction logic as the Excel tool.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform,
                fields=schema_fields
            )

            # Enhanced prompt for data extraction (same as Excel tool)
            enhanced_prompt = (
                f"\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                "{\n  \"data\": { ... },\n  \"missing_fields\": [ ... ]\n}\n\n"
                "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
            )

            print(f"\n📥 Generated system_prompt for preview:\n{enhanced_prompt}\n")

            # Prepare messages for extraction (same logic as Excel tool)
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history excluding system prompt and last message
            if messages and len(messages) > 2:
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages,
                max_tokens=4096,
                temperature=0
            )

            # Parse the response
            campaign_data = json.loads(response.choices[0].message.content)
            return campaign_data

        except json.JSONDecodeError:
            # Re-raise to match original error handling
            raise json.JSONDecodeError("JSON parsing failed", "", 0)
        except Exception as e:
            # Re-raise to match original error handling
            raise e

    async def _format_preview_content(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Format extracted campaign data into human-readable preview content.

        Args:
            platform: Advertising platform
            campaign_data: Extracted campaign data

        Returns:
            Formatted preview content string
        """
        if not campaign_data or "data" not in campaign_data:
            return "❌ 抽出されたデータが不完全です。"

        data = campaign_data["data"]

        # Load the platform format to get field descriptions
        format_file = self.excel_manager.platform_mapping.get(platform)
        output_format = await self.excel_manager._load_output_format(format_file)
        schema_fields = output_format.get("fields", {})

        # Get platform display name
        platform_display_name = self._get_platform_display_name(platform)

        # Build formatted preview
        preview_lines = [platform_display_name]

        # Format each field according to the requested format
        for field_name, field_config in schema_fields.items():
            field_value = data.get(field_name, "")

            # Handle nested fields (like Meta's carousel ads)
            if isinstance(field_config, dict) and "cards" in field_config:
                # This is a nested structure, handle separately
                preview_lines.append(self._format_nested_field(field_name, field_config, field_value))
                continue
            elif isinstance(field_config, dict) and any(isinstance(v, dict) for v in field_config.values()):
                # This is a nested structure without cards (like Meta's feed ads)
                preview_lines.append(self._format_nested_section(field_name, field_config, field_value))
                continue

            # Format regular field
            formatted_field = self._format_regular_field(field_name, field_config, field_value)
            preview_lines.append(formatted_field)

        return "\n".join(preview_lines)

    def _get_platform_display_name(self, platform: str) -> str:
        """Get the display name for the platform."""
        platform_names = {
            "LINE Ads": "LINE",
            "Meta (Instagram/Facebook)": "Meta",
            "YouTube Ads": "YouTube",
            "Google Search Ads": "Google検索広告",
            "Google Display Ads": "Googleディスプレイ広告",
            "Google Demand Gen Ads": "Googleディマンドジェン広告",
            "P-Max": "P-Max",
            "Ad Extensions": "広告表示オプション"
        }
        return platform_names.get(platform, platform)

    def _format_regular_field(self, field_name: str, field_config: Dict[str, Any], field_value: Any) -> str:
        """Format a regular field according to the requested format."""
        description = field_config.get("description", "")
        max_chars = field_config.get("max_chars")
        min_chars = field_config.get("min_chars")
        options = field_config.get("options", [])
        max_count = field_config.get("max_count")

        # Build the constraint description
        constraints = []

        if options:
            constraints.append(" / ".join(options))
        elif description:
            constraints.append(description)

        # Add character limits or count limits
        if max_count and max_count > 1:
            if max_chars:
                constraints.append(f"max {max_chars} chars")
        elif min_chars and max_chars:
            constraints.append(f"{min_chars}–{max_chars} chars")
        elif max_chars:
            constraints.append(f"max {max_chars} chars")
        elif min_chars:
            constraints.append(f"min {min_chars} chars")

        constraint_text = ", ".join(constraints) if constraints else ""

        # Handle special cases for specific fields
        if "エリア" in field_name and (not field_value or (isinstance(field_value, str) and not field_value.strip())):
            if constraint_text:
                constraint_text += "; 不明な場合は「入力内容を再度確認してください。」"
            else:
                constraint_text = "例: 日本全国; 不明な場合は「入力内容を再度確認してください。」"

        return f"■ {field_name}: [{constraint_text}]"

    def _format_nested_section(self, section_name: str, section_config: Dict[str, Any], section_value: Any) -> str:
        """Format a nested section (like Meta's feed ads)."""
        lines = [f"■ {section_name}:"]

        for field_name, field_config in section_config.items():
            if isinstance(field_config, dict) and "description" in field_config:
                field_value = ""
                if isinstance(section_value, dict):
                    field_value = section_value.get(field_name, "")

                formatted_field = self._format_regular_field(f"  {field_name}", field_config, field_value)
                lines.append(formatted_field.replace("■", "  ■"))

        return "\n".join(lines)

    def _format_nested_field(self, field_name: str, field_config: Dict[str, Any], field_value: Any) -> str:
        """Format a nested field with cards (like Meta's carousel ads)."""
        # field_value is kept for future use but not currently used in formatting
        max_count = field_config.get("max_count", 4)
        cards_config = field_config.get("cards", {})

        lines = [f"■ {field_name}: [Up to {max_count} cards]"]

        for card_field_name, card_field_config in cards_config.items():
            formatted_field = self._format_regular_field(f"  {card_field_name}", card_field_config, "")
            lines.append(formatted_field.replace("■", "  ■"))

        return "\n".join(lines)

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        This method reuses the same logic as the Excel tool.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し", "ヘッドライン", "headline", "タイトル",
            "説明文", "description", "広告文", "コピー",
            "キャンペーン", "campaign", "広告グループ",
            "ターゲット", "target", "配信", "予算",
            "キーワード", "keyword", "CTA", "行動喚起",
            "商品", "サービス", "ブランド", "企業",
            "広告", "ad", "プロモーション", "宣伝"
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

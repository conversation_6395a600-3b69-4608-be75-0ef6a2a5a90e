"""
Preview ad content tool for LLM function calling.

This tool generates a preview of extracted ad content for the LINE platform,
allowing users to review and refine content before exporting to Excel.
"""

from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class PreviewTool(BaseTool):
    """
    Tool for previewing ad content extracted from conversation history.
    
    This tool handles data extraction and formatting for preview display,
    reusing the same logic as the Excel creation tool but outputting
    human-readable formatted content instead of creating files.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the preview tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "preview_ad_content"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Generates a preview of extracted ad content for advertising platforms, formatted in a human-readable structure based on conversation history."

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max",
                        "Ad Extensions"
                    ]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute ad content preview generation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with formatted preview content
        """
        # Check if platform is supported by excel manager
        if platform not in self.excel_manager.platform_mapping:
            supported_platforms = "YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads・Ad Extensions"
            raise Exception(f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：{supported_platforms}")

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get('conversation_context', {})
            messages = conversation_context.get('messages', [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。プレビューを生成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「プレビューを再生成してください」とお伝えください。"
                )

            # Format the preview content directly from platform schema
            formatted_preview = await self._format_preview_content(platform)

            return {
                "preview_content": formatted_preview,
                "platform": platform,
                "extraction_status": "success"
            }

        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str:
                raise e
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ プレビュー生成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。"
                )

    async def _format_preview_content(self, platform: str) -> str:
        """
        Format preview content based on platform schema.

        Args:
            platform: Advertising platform

        Returns:
            Formatted preview content string
        """
        # Load the platform format to get field descriptions
        format_file = self.excel_manager.platform_mapping.get(platform)
        output_format = await self.excel_manager._load_output_format(format_file)
        schema_fields = output_format.get("fields", {})

        # Get platform display name
        platform_display_name = self._get_platform_display_name(platform)

        # Build formatted preview using standardized field names
        preview_lines = [platform_display_name]

        # Create standardized field mapping
        standardized_fields = self._create_standardized_fields(schema_fields)

        for field_name, field_description in standardized_fields.items():
            preview_lines.append(f"■ {field_name}: [{field_description}]")

        return "\n".join(preview_lines)

    def _create_standardized_fields(self, schema_fields: Dict[str, Any]) -> Dict[str, str]:
        """
        Create standardized field names and descriptions for all platforms.

        Args:
            schema_fields: Platform-specific schema fields

        Returns:
            Dictionary of standardized field names and descriptions
        """
        standardized = {}

        # Campaign name field
        campaign_field = self._find_field_by_keywords(schema_fields, ["キャンペーン", "campaign"])
        if campaign_field:
            field_config = schema_fields[campaign_field]
            char_range = self._get_char_range(field_config, "15–30")
            standardized["キャンペーン名"] = f"広告主 + 商品 + 目的, {char_range} chars"

        # Target/Group field
        target_field = self._find_field_by_keywords(schema_fields, ["グループ", "広告セット", "ターゲット", "group", "set"])
        if target_field:
            field_config = schema_fields[target_field]
            char_range = self._get_char_range(field_config, "10–25")
            standardized["ターゲット"] = f"商品またはオーディエンス, {char_range} chars"

        # Targeting conditions field
        conditions_field = self._find_field_by_keywords(schema_fields, ["配信条件", "配信ポイント", "targeting"])
        if conditions_field:
            field_config = schema_fields[conditions_field]
            char_range = self._get_char_range(field_config, "50–100")
            standardized["配信条件"] = f"年齢、性別、興味、地域など, {char_range} chars"

        # Age field
        age_field = self._find_field_by_keywords(schema_fields, ["年齢", "age"])
        if age_field:
            standardized["年齢層"] = "18–65 or specific range"

        # Gender field
        gender_field = self._find_field_by_keywords(schema_fields, ["性別", "gender"])
        if gender_field:
            standardized["性別"] = "全て / 男性 / 女性"

        # Area field
        area_field = self._find_field_by_keywords(schema_fields, ["エリア", "地域", "area"])
        if area_field:
            standardized["地域"] = "例: 日本全国; 不明な場合は「入力内容を再度確認してください。」"

        return standardized

    def _find_field_by_keywords(self, schema_fields: Dict[str, Any], keywords: list) -> str:
        """Find a field by matching keywords."""
        for field_name in schema_fields.keys():
            for keyword in keywords:
                if keyword in field_name:
                    return field_name
        return ""

    def _get_char_range(self, field_config: Dict[str, Any], default_range: str) -> str:
        """Get character range from field config or return default."""
        min_chars = field_config.get("min_chars")
        max_chars = field_config.get("max_chars")

        if min_chars and max_chars:
            return f"{min_chars}–{max_chars}"
        elif max_chars:
            return f"max {max_chars}"
        elif min_chars:
            return f"min {min_chars}"
        else:
            return default_range

    def _get_platform_display_name(self, platform: str) -> str:
        """Get the display name for the platform."""
        platform_names = {
            "LINE Ads": "LINE",
            "Meta (Instagram/Facebook)": "Meta",
            "YouTube Ads": "YouTube",
            "Google Search Ads": "Google検索広告",
            "Google Display Ads": "Googleディスプレイ広告",
            "Google Demand Gen Ads": "Googleディマンドジェン広告",
            "P-Max": "P-Max",
            "Ad Extensions": "広告表示オプション"
        }
        return platform_names.get(platform, platform)

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        This method reuses the same logic as the Excel tool.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し", "ヘッドライン", "headline", "タイトル",
            "説明文", "description", "広告文", "コピー",
            "キャンペーン", "campaign", "広告グループ",
            "ターゲット", "target", "配信", "予算",
            "キーワード", "keyword", "CTA", "行動喚起",
            "商品", "サービス", "ブランド", "企業",
            "広告", "ad", "プロモーション", "宣伝"
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

"""
Preview ad content tool for LLM function calling.

This tool generates a preview of extracted ad content for the LINE platform,
allowing users to review and refine content before exporting to Excel.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService

from .base_tool import BaseTool


class PreviewTool(BaseTool):
    """
    Tool for previewing ad content extracted from conversation history.

    This tool handles data extraction and formatting for preview display,
    reusing the same logic as the Excel creation tool but outputting
    human-readable formatted content instead of creating files.
    """

    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the preview tool.

        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()

    @property
    def name(self) -> str:
        """Return the tool name."""
        return "preview_ad_content"

    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Generates a preview of extracted ad content for advertising platforms, formatted in a human-readable structure based on conversation history."

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max",
                        "Ad Extensions",
                    ],
                }
            },
            "required": ["platform"],
        }

    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute ad content preview generation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with streaming response configuration
        """
        # Check if platform is supported by excel manager
        if platform not in self.excel_manager.platform_mapping:
            supported_platforms = "YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads・Ad Extensions"
            raise Exception(
                f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：{supported_platforms}"
            )

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get("conversation_context", {})
            messages = conversation_context.get("messages", [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。プレビューを生成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「プレビューを再生成してください」とお伝えください。"
                )

            # Extract campaign data from conversation using LLM
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Check for extraction errors
            if "error" in campaign_data:
                raise Exception("❌ データの抽出に失敗しました。広告コンテンツを確認してください。")

            # Format the extracted data into preview content
            formatted_preview = await self._format_preview_content(platform, campaign_data)

            # Store the preview information for followup response
            preview_info = {
                "platform": platform,
                "content": formatted_preview,
                "extraction_status": "success",
                "data_summary": self._create_data_summary(campaign_data),
            }

            # Return with followup_response instead of streaming
            return {
                "type": "followup_response",
                "followup_response": self._create_preview_followup_response(preview_info),
                "platform": platform,
                "extraction_status": "success",
                "preview_content": formatted_preview,
            }

        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str:
                raise e
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ プレビュー生成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。"
                )

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.

        This method reuses the same extraction logic as the Excel tool.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform, fields=schema_fields
            )

            # Enhanced prompt for data extraction (same as Excel tool)
            enhanced_prompt = (
                f"\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "For Google Search Ads or Google Display Ads, confirm whether the platform is Google or Yahoo based on user-provided data. If unclear, list 'platform_confirmation' in missing_fields. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                '{\n  "data": { ... },\n  "missing_fields": [ ... ]\n}\n\n'
                "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
            )

            print(f"\n📥 Generated system_prompt for preview:\n{enhanced_prompt}\n")

            # Prepare messages for extraction (same logic as Excel tool)
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history excluding system prompt and last message
            if messages and len(messages) > 2:
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages, max_tokens=4096, temperature=0
            )

            # Parse the response
            campaign_data = json.loads(response.choices[0].message.content)
            return campaign_data

        except json.JSONDecodeError:
            # Re-raise to match original error handling
            raise json.JSONDecodeError("JSON parsing failed", "", 0)
        except Exception as e:
            # Re-raise to match original error handling
            raise e

    async def _format_preview_content(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Format extracted campaign data into preview content.

        Args:
            platform: Advertising platform
            campaign_data: Extracted campaign data from LLM

        Returns:
            Formatted preview content string
        """
        if not campaign_data or "data" not in campaign_data:
            return "❌ 抽出されたデータが不完全です。"

        data = campaign_data["data"]

        # Load the platform format to get field descriptions
        format_file = self.excel_manager.platform_mapping.get(platform)
        output_format = await self.excel_manager._load_output_format(format_file)
        schema_fields = output_format.get("fields", {})

        # Get platform display name
        platform_display_name = self._get_platform_display_name(platform)

        # Build formatted preview using extracted data
        preview_lines = [platform_display_name]

        # Create standardized field mapping with actual values
        standardized_fields = self._create_standardized_fields_with_data(schema_fields, data)

        for field_name, field_value in standardized_fields.items():
            preview_lines.append(f"■ {field_name}: {field_value}")

        return "\n".join(preview_lines)

    def _create_data_summary(self, campaign_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of the extracted campaign data.

        Args:
            campaign_data: Extracted campaign data from LLM

        Returns:
            Summary information about the data
        """
        data = campaign_data.get("data", {})
        missing_fields = campaign_data.get("missing_fields", [])

        # Count different types of data
        total_fields = len(data)
        populated_fields = len([v for v in data.values() if v])

        # Analyze data complexity
        complex_fields = 0
        list_fields = 0
        for value in data.values():
            if isinstance(value, dict):
                complex_fields += 1
            elif isinstance(value, list):
                list_fields += 1

        return {
            "total_fields": total_fields,
            "populated_fields": populated_fields,
            "missing_fields_count": len(missing_fields),
            "complex_fields": complex_fields,
            "list_fields": list_fields,
            "missing_fields": missing_fields,
        }

    def _create_preview_followup_response(self, preview_info: Dict[str, Any]) -> str:
        """
        Create a followup response message for the preview.

        Args:
            preview_info: Information about the generated preview

        Returns:
            Followup response message
        """
        platform = preview_info["platform"]
        content = preview_info["content"]
        data_summary = preview_info["data_summary"]

        # Create the response message
        response_parts = []

        # Add platform-specific header
        response_parts.append(f"📋 **{platform} 広告プレビュー**")
        response_parts.append("")

        # Add the formatted preview content
        response_parts.append("```")
        response_parts.append(content)
        response_parts.append("```")
        response_parts.append("")

        # Add data summary
        response_parts.append("📊 **データ概要:**")
        response_parts.append(f"• 総フィールド数: {data_summary['total_fields']}")
        response_parts.append(f"• 入力済みフィールド: {data_summary['populated_fields']}")

        if data_summary["missing_fields_count"] > 0:
            response_parts.append(f"• 未入力フィールド: {data_summary['missing_fields_count']}")
            if data_summary["missing_fields"]:
                missing_list = ", ".join(data_summary["missing_fields"][:3])
                if len(data_summary["missing_fields"]) > 3:
                    missing_list += f" など{len(data_summary['missing_fields'])}件"
                response_parts.append(f"  ({missing_list})")

        if data_summary["complex_fields"] > 0:
            response_parts.append(f"• 複雑な構造: {data_summary['complex_fields']}フィールド")

        if data_summary["list_fields"] > 0:
            response_parts.append(f"• リスト形式: {data_summary['list_fields']}フィールド")

        response_parts.append("")

        # Add next steps
        response_parts.append("✅ **次のステップ:**")
        response_parts.append("• 内容を確認して、必要に応じて修正してください")
        response_parts.append("• Excelファイルを生成する場合は「Excelファイルを作成してください」とお伝えください")
        response_parts.append("• 他のプラットフォームのプレビューが必要な場合はお知らせください")

        return "\n".join(response_parts)

    def _create_standardized_fields_with_data(
        self, schema_fields: Dict[str, Any], data: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Create output fields with actual extracted data values for all platforms.

        This function recursively processes all nested structures until reaching
        actual string values, ensuring complete information extraction.

        Args:
            schema_fields: Platform-specific schema fields from JSON file
            data: Extracted campaign data

        Returns:
            Dictionary of field names and their formatted values
        """
        formatted_fields = {}

        # Process all fields from the schema
        for field_name, field_config in schema_fields.items():
            # Get the actual value from extracted data
            actual_value = data.get(field_name, "")

            # Recursively extract all nested content
            extracted_fields = self._recursive_field_extraction(field_name, actual_value, field_config)
            formatted_fields.update(extracted_fields)

        return formatted_fields

    def _recursive_field_extraction(self, field_name: str, value: Any, field_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Recursively extract all nested content until reaching actual string values.

        Args:
            field_name: Name of the current field
            value: The value to process (can be any type)
            field_config: Configuration for the field

        Returns:
            Dictionary of all extracted field-value pairs
        """
        extracted_fields = {}

        # Base case: if value is None, empty, or a simple type (str, int, float, bool)
        if value is None or value == "" or isinstance(value, (str, int, float, bool)):
            if value or value == 0 or value is False:  # Include 0 and False as valid values
                formatted_value = self._format_simple_value(value, field_config)
                extracted_fields[field_name] = formatted_value
            else:
                # Create placeholder for empty values
                placeholder = self._create_field_placeholder(
                    field_config, field_config.get("description", ""), field_config.get("options", [])
                )
                extracted_fields[field_name] = placeholder
            return extracted_fields

        # Recursive case: handle complex structures
        if isinstance(value, dict):
            # Process dictionary recursively
            for sub_key, sub_value in value.items():
                sub_field_name = f"{field_name} - {sub_key}"
                sub_extracted = self._recursive_field_extraction(sub_field_name, sub_value, {})
                extracted_fields.update(sub_extracted)

        elif isinstance(value, list):
            # Process list recursively - each item on separate line
            if not value:
                extracted_fields[field_name] = "[Empty list]"
            else:
                # Process each list item separately with individual character counts
                for i, item in enumerate(value):
                    item_field_name = f"{field_name} - Item {i + 1}"

                    if isinstance(item, (str, int, float, bool)):
                        # Simple item - format with individual character count
                        item_str = str(item)
                        if field_config.get("max_chars") or field_config.get("min_chars"):
                            char_count = len(item_str)
                            char_info = self._get_char_range_info(field_config, char_count)
                            formatted_item = f"{item_str} {char_info}"
                        else:
                            formatted_item = item_str
                        extracted_fields[item_field_name] = formatted_item
                    else:
                        # Complex item - process recursively
                        item_extracted = self._recursive_field_extraction(item_field_name, item, {})
                        extracted_fields.update(item_extracted)

        else:
            # Handle any other type as string
            formatted_value = self._format_simple_value(str(value), field_config)
            extracted_fields[field_name] = formatted_value

        return extracted_fields

    def _format_complex_field(
        self, field_name: str, value: Any, field_config: Dict[str, Any], indent_level: int = 0
    ) -> Any:
        """
        Format complex field structures into hierarchical text with proper indentation.

        Args:
            field_name: Name of the field
            value: The actual value (can be dict, list, string, etc.)
            field_config: Configuration for the field
            indent_level: Current indentation level

        Returns:
            Formatted field content (string or dict of multiple fields)
        """
        # Handle empty or None values
        if not value and value != 0:
            return self._create_field_placeholder(
                field_config, field_config.get("description", ""), field_config.get("options", [])
            )

        # Handle different value types
        if isinstance(value, dict):
            return self._format_dict_hierarchical(field_name, value, indent_level)
        elif isinstance(value, list):
            return self._format_list_hierarchical(value, field_config, indent_level)
        else:
            # Handle simple values (string, number, boolean)
            return self._format_simple_value(value, field_config)

    def _format_dict_hierarchical(
        self, parent_name: str, dict_value: Dict[str, Any], indent_level: int
    ) -> Dict[str, str]:
        """
        Format dictionary values into hierarchical structure with indentation.

        Args:
            parent_name: Name of the parent field
            dict_value: Dictionary to format
            indent_level: Current indentation level

        Returns:
            Dictionary of formatted fields with proper hierarchy
        """
        formatted_fields = {}
        indent = "  " * indent_level  # 2 spaces per level

        for key, value in dict_value.items():
            field_key = f"{parent_name} - {key}" if parent_name else key

            if isinstance(value, dict):
                # Nested dictionary - add header and recurse
                formatted_fields[field_key] = f"{indent}[Object with {len(value)} properties]"
                nested_fields = self._format_dict_hierarchical(field_key, value, indent_level + 1)
                formatted_fields.update(nested_fields)
            elif isinstance(value, list):
                # List within dictionary
                list_content = self._format_list_content(value, indent_level + 1)
                formatted_fields[field_key] = f"{indent}{list_content}"
            else:
                # Simple value
                formatted_fields[field_key] = f"{indent}{value}"

        return formatted_fields

    def _format_list_hierarchical(self, list_value: list, field_config: Dict[str, Any], indent_level: int) -> str:
        """
        Format list values into hierarchical structure.

        Args:
            list_value: List to format
            field_config: Field configuration
            indent_level: Current indentation level

        Returns:
            Formatted list content as string
        """
        if not list_value:
            return "[Empty list]"

        indent = "  " * indent_level
        max_count = field_config.get("max_count")

        # Check if list contains complex objects
        if any(isinstance(item, (dict, list)) for item in list_value):
            # Complex list - format each item on new line
            lines = [f"{indent}[List with {len(list_value)} items]"]

            display_count = min(len(list_value), max_count) if max_count else len(list_value)

            for i, item in enumerate(list_value[:display_count]):
                item_indent = "  " * (indent_level + 1)
                if isinstance(item, dict):
                    lines.append(f"{item_indent}Item {i + 1}:")
                    for sub_key, sub_value in item.items():
                        if isinstance(sub_value, (dict, list)):
                            lines.append(f"{item_indent}  {sub_key}: [Complex object]")
                        else:
                            lines.append(f"{item_indent}  {sub_key}: {sub_value}")
                elif isinstance(item, list):
                    lines.append(f"{item_indent}Item {i + 1}: {self._format_list_content(item, indent_level + 2)}")
                else:
                    lines.append(f"{item_indent}Item {i + 1}: {item}")

            if max_count and len(list_value) > max_count:
                lines.append(f"{indent}  ... and {len(list_value) - max_count} more items")

            return "\n".join(lines)
        else:
            # Simple list - format inline
            return self._format_list_content(list_value, indent_level)

    def _format_list_content(self, list_value: list, indent_level: int) -> str:
        """Format simple list content."""
        if not list_value:
            return "[Empty]"

        # For simple lists, join with commas
        if all(not isinstance(item, (dict, list)) for item in list_value):
            return ", ".join(str(item) for item in list_value)

        # For complex lists, format each item
        indent = "  " * indent_level
        items = []
        for i, item in enumerate(list_value):
            if isinstance(item, dict):
                items.append(f"{indent}[Object {i + 1}]")
            elif isinstance(item, list):
                items.append(f"{indent}[List {i + 1}]")
            else:
                items.append(f"{indent}{item}")

        return "\n".join(items)

    def _format_simple_value(self, value: Any, field_config: Dict[str, Any]) -> str:
        """Format simple values with character count information if applicable."""
        formatted_value = str(value)

        # Add character count if applicable
        max_chars = field_config.get("max_chars")
        min_chars = field_config.get("min_chars")

        if max_chars or min_chars:
            char_count = len(formatted_value)
            char_info = self._get_char_range_info(field_config, char_count)
            return f"{formatted_value} {char_info}"

        return formatted_value

    def _is_nested_structure(self, field_config: Dict[str, Any]) -> bool:
        """Check if a field configuration represents a nested structure."""
        # Check for carousel cards structure
        if "cards" in field_config:
            return True

        # Check for nested object structure (like Meta's feed ads)
        if any(isinstance(v, dict) and "description" in v for v in field_config.values()):
            return True

        return False

    def _process_nested_field(
        self, field_name: str, field_config: Dict[str, Any], data: Dict[str, Any]
    ) -> Dict[str, str]:
        """Process nested field structures like carousel ads or feed ads."""
        nested_fields = {}

        # Handle carousel cards structure
        if "cards" in field_config:
            max_count = field_config.get("max_count", 4)
            nested_fields[field_name] = f"[Up to {max_count} cards]"

            # Process each card field
            cards_config = field_config.get("cards", {})
            for card_field_name, card_field_config in cards_config.items():
                card_value = self._format_field_with_data(f"  {card_field_name}", card_field_config, {})
                nested_fields[f"  {card_field_name}"] = card_value

        # Handle nested object structure (like Meta's feed ads)
        else:
            for sub_field_name, sub_field_config in field_config.items():
                if isinstance(sub_field_config, dict) and "description" in sub_field_config:
                    # Get nested data
                    nested_data = data.get(field_name, {}) if isinstance(data.get(field_name), dict) else {}
                    sub_value = self._format_field_with_data(sub_field_name, sub_field_config, nested_data)
                    nested_fields[f"{field_name} - {sub_field_name}"] = sub_value

        return nested_fields

    def _flatten_dict_value(self, field_name: str, dict_value: Dict[str, Any]) -> Dict[str, str]:
        """
        Flatten dictionary values into individual ■ key: value lines.

        Args:
            field_name: The parent field name
            dict_value: The dictionary value to flatten

        Returns:
            Dictionary of flattened field names and their values
        """
        flattened_fields = {}

        for sub_key, sub_value in dict_value.items():
            # Create the flattened field name
            flattened_key = f"{field_name} - {sub_key}"

            # Format the sub-value
            if isinstance(sub_value, dict):
                # Recursively flatten nested dictionaries
                nested_flattened = self._flatten_dict_value(flattened_key, sub_value)
                flattened_fields.update(nested_flattened)
            elif isinstance(sub_value, list):
                # Handle list values
                formatted_value = ", ".join(str(item) for item in sub_value)
                flattened_fields[flattened_key] = formatted_value
            else:
                # Handle simple values
                flattened_fields[flattened_key] = str(sub_value)

        return flattened_fields

    def _format_field_with_data(self, field_name: str, field_config: Dict[str, Any], data: Dict[str, Any]) -> str:
        """Format a single field with its actual data or placeholder."""
        # Get the actual value from extracted data
        actual_value = data.get(field_name, "")

        # Get field configuration details
        description = field_config.get("description", "")
        max_chars = field_config.get("max_chars")
        min_chars = field_config.get("min_chars")
        options = field_config.get("options", [])
        max_count = field_config.get("max_count")

        # Format the value
        if actual_value:
            # Handle different value types
            if isinstance(actual_value, dict):
                # For dict values, we'll handle them in the main function
                # This method should not be called for dict values
                return "[Dictionary - handled separately]"
            elif isinstance(actual_value, list):
                if max_count and len(actual_value) > max_count:
                    formatted_value = f"{', '.join(str(item) for item in actual_value[:max_count])} (showing {max_count} of {len(actual_value)})"
                else:
                    formatted_value = ", ".join(str(item) for item in actual_value)
            else:
                formatted_value = str(actual_value)

            # Add character count if applicable
            if max_chars or min_chars:
                char_count = len(formatted_value)
                char_info = self._get_char_range_info(field_config, char_count)
                return f"{formatted_value} {char_info}"
            else:
                return formatted_value

        else:
            # Create placeholder when no data is available
            return self._create_field_placeholder(field_config, description, options)

    def _get_char_range_info(self, field_config: Dict[str, Any], actual_count: int) -> str:
        """Get character range information with actual count."""
        min_chars = field_config.get("min_chars")
        max_chars = field_config.get("max_chars")

        if min_chars and max_chars:
            status = "✓" if min_chars <= actual_count <= max_chars else "⚠️"
            return f"[{actual_count}/{min_chars}–{max_chars} chars {status}]"
        elif max_chars:
            status = "✓" if actual_count <= max_chars else "⚠️"
            return f"[{actual_count}/{max_chars} chars {status}]"
        elif min_chars:
            status = "✓" if actual_count >= min_chars else "⚠️"
            return f"[{actual_count}/{min_chars}+ chars {status}]"
        else:
            return f"[{actual_count} chars]"

    def _create_field_placeholder(self, field_config: Dict[str, Any], description: str, options: list) -> str:
        """Create appropriate placeholder for missing data."""
        # Use options if available
        if options:
            return f"[{' / '.join(options)}]"

        # Use description if available
        if description:
            char_range = self._get_char_range(field_config, "")
            if char_range:
                return f"[{description}, {char_range} chars]"
            else:
                return f"[{description}]"

        # Default placeholder
        char_range = self._get_char_range(field_config, "")
        if char_range:
            return f"[未設定, {char_range} chars]"
        else:
            return "[未設定]"

    def _find_field_by_keywords(self, schema_fields: Dict[str, Any], keywords: list) -> str:
        """Find a field by matching keywords."""
        for field_name in schema_fields.keys():
            for keyword in keywords:
                if keyword in field_name:
                    return field_name
        return ""

    def _get_char_range(self, field_config: Dict[str, Any], default_range: str) -> str:
        """Get character range from field config or return default."""
        min_chars = field_config.get("min_chars")
        max_chars = field_config.get("max_chars")

        if min_chars and max_chars:
            return f"{min_chars}–{max_chars}"
        elif max_chars:
            return f"max {max_chars}"
        elif min_chars:
            return f"min {min_chars}"
        else:
            return default_range

    def _get_platform_display_name(self, platform: str) -> str:
        """Get the display name for the platform."""
        platform_names = {
            "LINE Ads": "LINE",
            "Meta (Instagram/Facebook)": "Meta",
            "YouTube Ads": "YouTube",
            "Google Search Ads": "Google検索広告",
            "Google Display Ads": "Googleディスプレイ広告",
            "Google Demand Gen Ads": "Googleディマンドジェン広告",
            "P-Max": "P-Max",
            "Ad Extensions": "広告表示オプション",
        }
        return platform_names.get(platform, platform)

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        This method reuses the same logic as the Excel tool.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し",
            "ヘッドライン",
            "headline",
            "タイトル",
            "説明文",
            "description",
            "広告文",
            "コピー",
            "キャンペーン",
            "campaign",
            "広告グループ",
            "ターゲット",
            "target",
            "配信",
            "予算",
            "キーワード",
            "keyword",
            "CTA",
            "行動喚起",
            "商品",
            "サービス",
            "ブランド",
            "企業",
            "広告",
            "ad",
            "プロモーション",
            "宣伝",
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

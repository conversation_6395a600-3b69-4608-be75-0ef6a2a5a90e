"""
Preview ad content tool for LLM function calling.

This tool generates a preview of extracted ad content for the LINE platform,
allowing users to review and refine content before exporting to Excel.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class PreviewTool(BaseTool):
    """
    Tool for previewing ad content extracted from conversation history.
    
    This tool handles data extraction and formatting for preview display,
    reusing the same logic as the Excel creation tool but outputting
    human-readable formatted content instead of creating files.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the preview tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "preview_ad_content"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Generates a preview of extracted ad content for the LINE platform, formatted in a human-readable structure based on conversation history."
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": "The advertising platform. Must be 'LINE Ads' for this tool.",
                    "enum": ["LINE Ads"]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute ad content preview generation.

        Args:
            platform: Advertising platform name (must be "LINE Ads")
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with formatted preview content
        """
        # Validate platform
        if platform != "LINE Ads":
            raise Exception("❌ このプレビューツールは「LINE Ads」プラットフォームのみに対応しています。")

        # Check if platform is supported by excel manager
        if platform not in self.excel_manager.platform_mapping:
            raise Exception("❌ 指定されたプラットフォームは対応していません。")

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get('conversation_context', {})
            messages = conversation_context.get('messages', [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。プレビューを生成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「プレビューを再生成してください」とお伝えください。"
                )

            # Extract campaign data from conversation
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Check for extraction errors
            if "error" in campaign_data:
                raise Exception("❌ データの抽出に失敗しました。広告コンテンツを確認してください。")

            # Format the extracted data for preview
            formatted_preview = await self._format_preview_content(platform, campaign_data)

            return {
                "preview_content": formatted_preview,
                "platform": platform,
                "extraction_status": "success"
            }

        except json.JSONDecodeError as e:
            raise Exception(
                "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                "広告コンテンツを再作成してから、もう一度プレビューの生成をお試しください。"
            )
        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str or "LINE Ads" in error_str:
                raise e
            elif "JSON" in error_str or "解析" in error_str:
                raise Exception(
                    "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                    "広告コンテンツを再作成してから、もう一度プレビューの生成をお試しください。"
                )
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ プレビュー生成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。"
                )

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.
        
        This method reuses the same extraction logic as the Excel tool.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform,
                fields=schema_fields
            )

            # Enhanced prompt for data extraction (same as Excel tool)
            enhanced_prompt = (
                f"\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                "{\n  \"data\": { ... },\n  \"missing_fields\": [ ... ]\n}\n\n"
                "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
            )

            print(f"\n📥 Generated system_prompt for preview:\n{enhanced_prompt}\n")

            # Prepare messages for extraction (same logic as Excel tool)
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history excluding system prompt and last message
            if messages and len(messages) > 2:
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages,
                max_tokens=4096,
                temperature=0
            )

            # Parse the response
            campaign_data = json.loads(response.choices[0].message.content)
            return campaign_data

        except json.JSONDecodeError:
            # Re-raise to match original error handling
            raise json.JSONDecodeError("JSON parsing failed", "", 0)
        except Exception as e:
            # Re-raise to match original error handling
            raise e

    async def _format_preview_content(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Format extracted campaign data into human-readable preview content.

        Args:
            platform: Advertising platform
            campaign_data: Extracted campaign data

        Returns:
            Formatted preview content string
        """
        if not campaign_data or "data" not in campaign_data:
            return "❌ 抽出されたデータが不完全です。"

        data = campaign_data["data"]
        missing_fields = campaign_data.get("missing_fields", [])

        # Load the platform format to get field descriptions
        format_file = self.excel_manager.platform_mapping.get(platform)
        output_format = await self.excel_manager._load_output_format(format_file)
        schema_fields = output_format.get("fields", {})

        # Build formatted preview
        preview_lines = [
            f"🎯 **{platform} 広告コンテンツ プレビュー**",
            "",
            "📋 **抽出されたデータ:**"
        ]

        # Format each field with its description and constraints
        for field_name, field_config in schema_fields.items():
            field_value = data.get(field_name, "")
            description = field_config.get("description", "")
            max_chars = field_config.get("max_chars")
            required = field_config.get("required", False)

            # Format field header
            status_icon = "✅" if field_value else "❌"
            required_text = " (必須)" if required else ""
            char_limit_text = f" (最大{max_chars}文字)" if max_chars else ""

            preview_lines.append(f"\n**{field_name}**{required_text}{char_limit_text}")
            if description:
                preview_lines.append(f"  💡 {description}")

            # Format field value
            if field_value:
                if isinstance(field_value, list):
                    for i, item in enumerate(field_value, 1):
                        preview_lines.append(f"  {status_icon} {i}. {item}")
                else:
                    # Check character limit
                    char_count = len(str(field_value))
                    char_info = f" ({char_count}文字)" if max_chars else ""
                    char_warning = " ⚠️ 文字数超過" if max_chars and char_count > max_chars else ""
                    preview_lines.append(f"  {status_icon} {field_value}{char_info}{char_warning}")
            else:
                preview_lines.append(f"  {status_icon} (未設定)")

        # Add missing fields section if any
        if missing_fields:
            preview_lines.extend([
                "",
                "⚠️ **不足しているフィールド:**"
            ])
            for field in missing_fields:
                preview_lines.append(f"  • {field}")

        # Add summary
        total_fields = len(schema_fields)
        filled_fields = sum(1 for field_name in schema_fields.keys() if data.get(field_name))
        completion_rate = int((filled_fields / total_fields) * 100) if total_fields > 0 else 0

        preview_lines.extend([
            "",
            "📊 **完成度:**",
            f"  {filled_fields}/{total_fields} フィールド完成 ({completion_rate}%)",
            "",
            "💡 **次のステップ:**",
            "  • 不足している情報があれば追加で指定してください",
            "  • 内容に問題がなければ「Excelファイルを作成してください」とお伝えください",
            "  • 修正が必要な場合は具体的な変更内容をお伝えください"
        ])

        return "\n".join(preview_lines)

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        This method reuses the same logic as the Excel tool.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し", "ヘッドライン", "headline", "タイトル",
            "説明文", "description", "広告文", "コピー",
            "キャンペーン", "campaign", "広告グループ",
            "ターゲット", "target", "配信", "予算",
            "キーワード", "keyword", "CTA", "行動喚起",
            "商品", "サービス", "ブランド", "企業",
            "広告", "ad", "プロモーション", "宣伝"
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

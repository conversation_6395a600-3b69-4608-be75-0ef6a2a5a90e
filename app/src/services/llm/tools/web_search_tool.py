"""
Web search tool for LLM function calling.

This tool provides web search functionality using the search service.
"""

from typing import Any, Dict

from app.src.services.search_service import SearchService

from .base_tool import BaseTool


class WebSearchTool(BaseTool):
    """
    Tool for performing web searches.

    This tool integrates with the search service to provide web search
    functionality for the LLM.
    """

    def __init__(self, search_service: SearchService):
        """
        Initialize the web search tool.

        Args:
            search_service: Search service instance
        """
        self.search_service = search_service

    @property
    def name(self) -> str:
        """Return the tool name."""
        return "web_search"

    @property
    def description(self) -> str:
        """Return the tool description."""
        return (
            "Search the web for current information related to companies, products, "
            "industry trends, or social buzz in Japan. Used to find advertising context, "
            "homepage content, or what is currently trending in the market."
        )

    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query string",
                }
            },
            "required": ["query"],
        }

    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Execute web search.

        Args:
            query: Search query string
            **kwargs: Additional search parameters

        Returns:
            Search results
        """
        return await self.search_service.search(query, **kwargs)

"""
Data processing utilities for LLM operations.

This module provides utility functions for data extraction, validation,
and transformation operations.
"""

from typing import Any, Dict, List


class DataUtils:
    """
    Utility class for data processing operations.
    """

    @staticmethod
    def get_field_value(field_data: Any, default: Any = "") -> Any:
        """
        Extract field value from data structure.

        Args:
            field_data: Field data (can be dict with 'value' key or direct value)
            default: Default value if field is None or empty

        Returns:
            Extracted field value
        """
        if isinstance(field_data, dict) and "value" in field_data:
            return field_data.get("value", default)
        return field_data if field_data is not None else default

    @staticmethod
    def ensure_list(value: Any, max_items: int = None) -> List[Any]:
        """
        Ensure value is a list with optional max items limit.

        Args:
            value: Value to convert to list
            max_items: Maximum number of items to return

        Returns:
            List representation of the value
        """
        if not isinstance(value, list):
            result = [value] if value else []
        else:
            result = value

        if max_items is not None:
            result = result[:max_items]

        return result

    @staticmethod
    def get_nested_value(data: Dict[str, Any], path: str, default: Any = "") -> Any:
        """
        Get nested value from dictionary using dot notation.

        Args:
            data: Dictionary to search
            path: Dot-separated path (e.g., "ads.meta.headline")
            default: Default value if path not found

        Returns:
            Value at the specified path
        """
        keys = path.split(".")
        current = data

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default

        return DataUtils.get_field_value(current, default)

    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """
        Validate that required fields are present and not empty.

        Args:
            data: Data dictionary to validate
            required_fields: List of required field paths

        Returns:
            List of missing field paths
        """
        missing_fields = []

        for field_path in required_fields:
            value = DataUtils.get_nested_value(data, field_path)
            if not value or (isinstance(value, str) and not value.strip()):
                missing_fields.append(field_path)

        return missing_fields

    @staticmethod
    def sanitize_text(text: str, max_length: int = None) -> str:
        """
        Sanitize text by removing extra whitespace and limiting length.

        Args:
            text: Text to sanitize
            max_length: Maximum length to truncate to

        Returns:
            Sanitized text
        """
        if not isinstance(text, str):
            text = str(text)

        # Remove extra whitespace
        text = " ".join(text.split())

        # Truncate if necessary
        if max_length and len(text) > max_length:
            text = text[:max_length].rstrip()

        return text

    @staticmethod
    def merge_dictionaries(*dicts: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge multiple dictionaries with deep merging for nested dicts.

        Args:
            *dicts: Dictionaries to merge

        Returns:
            Merged dictionary
        """
        result = {}

        for d in dicts:
            for key, value in d.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = DataUtils.merge_dictionaries(result[key], value)
                else:
                    result[key] = value

        return result

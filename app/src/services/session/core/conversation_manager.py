"""Conversation management functionality."""

import uuid
from typing import List

from fastapi_base.error_code import Server<PERSON><PERSON>rC<PERSON>
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models.chat_sessions import ChatConversation
from app.src.repositories.chat_session import ChatConversationRepository
from app.src.schemas.chat_sessions import ChatConversationCreate, ChatHistoryItem


class ConversationManager:
    """Manages conversation operations within chat sessions."""

    def __init__(self):
        self.chat_conversation_repository = ChatConversationRepository(ChatConversation)

    async def add_conversation(
        self, db_session: AsyncSession, session_id: uuid.UUID, question: str, response: str
    ) -> bool:
        """Add a conversation pair (question and response) to a session.

        Args:
            db_session: Database session
            session_id: ID of the chat session
            question: User's question
            response: Assistant's response

        Returns:
            True if successful, False otherwise

        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            # Create user conversation entry
            user_conversation = ChatConversationCreate(
                session_id=session_id,
                role="user",
                content=question,
            )
            await self.chat_conversation_repository.create(db_session, obj_in=user_conversation)

            # Create assistant conversation entry
            assistant_conversation = ChatConversationCreate(
                session_id=session_id,
                role="assistant",
                content=response,
            )
            await self.chat_conversation_repository.create(db_session, obj_in=assistant_conversation)

            return True
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_conversation_history(self, db_session: AsyncSession, session_id: uuid.UUID) -> List[ChatHistoryItem]:
        """Get conversation history for a session.

        Args:
            db_session: Database session
            session_id: ID of the chat session

        Returns:
            List of conversation history items

        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            conversations = await self.chat_conversation_repository.get_conversations_by_session_id(
                db_session, session_id
            )
            return [
                ChatHistoryItem(role=conv.role, content=conv.content, timestamp=conv.timestamp)
                for conv in conversations
                if not conv.is_deleted and not conv.is_summarized
            ]
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def mark_conversations_as_summarized(self, db_session: AsyncSession, session_id: uuid.UUID) -> None:
        """Mark conversations as summarized.

        Args:
            db_session: Database session
            session_id: ID of the chat session

        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            await self.chat_conversation_repository.update_conversation_summary(db_session, session_id)
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    def extract_conversation_content(self, conversations: List[ChatHistoryItem]) -> List[str]:
        """Extract content from conversation history.

        Args:
            conversations: List of conversation history items

        Returns:
            List of conversation content strings
        """
        return [conv.content for conv in conversations]

    def format_conversation_text(self, conversations: List[ChatHistoryItem]) -> str:
        """Format conversations as a single text string.

        Args:
            conversations: List of conversation history items

        Returns:
            Formatted conversation text
        """
        return "\n".join([f"{conv.role}: {conv.content}" for conv in conversations])

"""Session cleanup and maintenance operations."""

from fastapi_base.error_code import Server<PERSON><PERSON>r<PERSON><PERSON>
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models.chat_sessions import ChatSession
from app.src.repositories.chat_session import ChatSessionRepository


class CleanupManager:
    """Manages cleanup and maintenance operations for chat sessions."""

    def __init__(self):
        self.chat_session_repository = ChatSessionRepository(ChatSession)

    async def clean_old_sessions(self, db_session: AsyncSession, days_threshold: int = 30) -> int:
        """Clean up old sessions that exceed the age threshold.

        Args:
            db_session: Database session
            days_threshold: Number of days after which sessions are considered old

        Returns:
            Number of sessions cleaned up

        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            count = await self.chat_session_repository.clean_old_sessions(db_session, days_threshold)
            await db_session.commit()
            return count
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

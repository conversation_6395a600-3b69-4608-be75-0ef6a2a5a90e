"""Session and conversation summarization management."""

import uuid
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession

from app.src.services.session.core.conversation_manager import ConversationManager
from app.src.services.session.core.session_manager import SessionManager


class SummaryManager:
    """Manages summarization of sessions and conversations using LLM services."""

    # Summary prompts
    SESSION_SUMMARY_PROMPT = (
        "Summarize the user's question from the chat history in 5–6 Japanese words. "
        "Keep it short, clear, and suitable for display as a sidebar session title."
    )
    CONVERSATION_SUMMARY_PROMPT = "Summarize chat history conversation"

    def __init__(self):
        self.session_manager = SessionManager()
        self.conversation_manager = ConversationManager()

    async def generate_session_summary(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_messages: List[str],
    ) -> str:
        """Generate and save session summary.

        Args:
            llm_service: LLM service instance
            db_session: Database session
            session_id: ID of the chat session
            user_id: ID of the user
            request: Request object with model parameters
            chat_messages: List of chat messages to summarize

        Returns:
            Generated summary text
        """
        summary = await self._generate_summary(llm_service, request, chat_messages, self.SESSION_SUMMARY_PROMPT)

        await self.session_manager.update_session_summary(db_session, user_id, session_id, summary)

        return summary

    async def generate_conversation_summary(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_messages: List[str],
    ) -> str:
        """Generate and save conversation summary.

        Args:
            llm_service: LLM service instance
            db_session: Database session
            session_id: ID of the chat session
            user_id: ID of the user
            request: Request object with model parameters
            chat_messages: List of chat messages to summarize

        Returns:
            Generated summary text
        """
        summary = await self._generate_summary(llm_service, request, chat_messages, self.CONVERSATION_SUMMARY_PROMPT)

        await self.session_manager.update_conversation_summary(db_session, user_id, session_id, summary)

        await self.conversation_manager.mark_conversations_as_summarized(db_session, session_id)

        return summary

    async def _generate_summary(
        self,
        llm_service,
        request,
        chat_messages: List[str],
        system_prompt: str,
    ) -> str:
        """Generate summary using LLM service.

        Args:
            llm_service: LLM service instance
            request: Request object with model parameters
            chat_messages: List of chat messages to summarize
            system_prompt: System prompt for summarization

        Returns:
            Generated summary text
        """
        messages = [
            {
                "role": "system",
                "content": system_prompt,
            },
            {"role": "user", "content": " ".join([(msg + "\n") for msg in chat_messages])},
        ]

        # Use the LLM client through the service's llm_client attribute
        response = await llm_service.llm_client.create_chat_completion(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
        )

        return response.choices[0].message.content

    async def get_session_summary(self, db_session: AsyncSession, session_id: uuid.UUID) -> str:
        """Get existing session summary.

        Args:
            db_session: Database session
            session_id: ID of the chat session

        Returns:
            Session summary text
        """
        return await self.session_manager.get_summary_session(db_session, session_id)

"""Token counting and management utilities."""

from typing import List

import decouple
import tiktoken

from app.src.schemas.chat_sessions import ChatHistoryItem


class TokenManager:
    """Manages token counting and history truncation based on token limits."""

    DEFAULT_MODEL = "gpt-4.1-mini"
    DEFAULT_ENCODING = "cl100k_base"

    @staticmethod
    def count_tokens(text: str, model: str = None) -> int:
        """Count tokens in a text string.

        Args:
            text: Text to count tokens for
            model: Model name to get encoding for (defaults to DEFAULT_MODEL)

        Returns:
            Number of tokens in the text
        """
        if model is None:
            model = TokenManager.DEFAULT_MODEL

        try:
            enc = tiktoken.encoding_for_model(model)
        except KeyError:
            enc = tiktoken.get_encoding(TokenManager.DEFAULT_ENCODING)
        return len(enc.encode(text))

    @classmethod
    def get_max_token_limit(cls) -> int:
        """Get the maximum token limit from configuration.

        Returns:
            Maximum token limit
        """
        return int(decouple.config("MAX_TOKENS_LENGTH", default=4000))

    @classmethod
    def calculate_total_tokens(cls, chat_history: List[ChatHistoryItem], new_question: str) -> int:
        """Calculate total tokens for chat history plus new question.

        Args:
            chat_history: List of conversation history items
            new_question: New question to add

        Returns:
            Total token count
        """
        history_text = "\n".join([f"{msg.role}: {msg.content}" for msg in chat_history])
        total_text = history_text + f"\nuser: {new_question}"
        return cls.count_tokens(total_text)

    @classmethod
    def should_truncate_history(cls, chat_history: List[ChatHistoryItem], new_question: str) -> bool:
        """Check if chat history should be truncated due to token limit.

        Args:
            chat_history: List of conversation history items
            new_question: New question to add

        Returns:
            True if history should be truncated, False otherwise
        """
        total_tokens = cls.calculate_total_tokens(chat_history, new_question)
        return total_tokens > cls.get_max_token_limit()

    @classmethod
    def get_messages_to_summarize(
        cls, chat_history: List[ChatHistoryItem], keep_recent: int = 10
    ) -> List[ChatHistoryItem]:
        """Get messages that should be summarized to reduce token count.

        Args:
            chat_history: List of conversation history items
            keep_recent: Number of recent messages to keep unsummarized

        Returns:
            List of messages to summarize
        """
        if len(chat_history) <= keep_recent:
            return chat_history
        return chat_history[:-keep_recent]

    @classmethod
    def extract_content_for_summary(cls, messages: List[ChatHistoryItem]) -> List[str]:
        """Extract content from messages for summarization.

        Args:
            messages: List of conversation history items

        Returns:
            List of content strings
        """
        return [msg.content for msg in messages]

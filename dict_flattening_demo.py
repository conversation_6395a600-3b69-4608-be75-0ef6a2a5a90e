#!/usr/bin/env python3
"""
Demonstration of dictionary value flattening in the PreviewTool.

This script shows how dictionary values are flattened into ■ key: value format.
"""

def demonstrate_dict_flattening():
    """Demonstrate how dictionary flattening works."""
    
    print("🔧 Dictionary Value Flattening Demo")
    print("=" * 50)
    
    # Example 1: Simple dictionary
    print("\n📋 Example 1: Simple Dictionary")
    print("Input data:")
    simple_dict = {
        "フィード広告・ストーリー広告": {
            "見出し": "新商品登場！",
            "テキスト": "革新的な商品をお試しください",
            "URL": "https://example.com/product"
        }
    }
    print(f"  {simple_dict}")
    
    print("\nFlattened output:")
    print("■ フィード広告・ストーリー広告 - 見出し: 新商品登場！")
    print("■ フィード広告・ストーリー広告 - テキスト: 革新的な商品をお試しください")
    print("■ フィード広告・ストーリー広告 - URL: https://example.com/product")
    
    # Example 2: Nested dictionary
    print("\n📋 Example 2: Nested Dictionary")
    print("Input data:")
    nested_dict = {
        "ターゲティング": {
            "デモグラフィック": {
                "年齢": "20-30",
                "性別": "全て"
            },
            "興味関心": {
                "カテゴリ": ["美容", "健康"],
                "キーワード": ["新商品", "おすすめ"]
            }
        }
    }
    print(f"  {nested_dict}")
    
    print("\nFlattened output:")
    print("■ ターゲティング - デモグラフィック - 年齢: 20-30")
    print("■ ターゲティング - デモグラフィック - 性別: 全て")
    print("■ ターゲティング - 興味関心 - カテゴリ: 美容, 健康")
    print("■ ターゲティング - 興味関心 - キーワード: 新商品, おすすめ")
    
    # Example 3: Mixed data types
    print("\n📋 Example 3: Mixed Data Types")
    print("Input data:")
    mixed_dict = {
        "カルーセル広告": {
            "カード数": 3,
            "カード1": {
                "見出し": "商品A",
                "説明": "商品Aの説明",
                "価格": 1000
            },
            "設定": ["自動配置", "モバイル最適化"]
        }
    }
    print(f"  {mixed_dict}")
    
    print("\nFlattened output:")
    print("■ カルーセル広告 - カード数: 3")
    print("■ カルーセル広告 - カード1 - 見出し: 商品A")
    print("■ カルーセル広告 - カード1 - 説明: 商品Aの説明")
    print("■ カルーセル広告 - カード1 - 価格: 1000")
    print("■ カルーセル広告 - 設定: 自動配置, モバイル最適化")

def show_before_after_comparison():
    """Show before and after comparison."""
    
    print("\n\n📊 Before vs After Comparison")
    print("=" * 60)
    
    print("\n🔴 Before (Nested Structure):")
    before_output = """Meta
■ キャンペーン名: テストキャンペーン
■ フィード広告・ストーリー広告: [Nested Object]
  ■ 見出し: 新商品登場！
  ■ テキスト: 革新的な商品をお試しください
  ■ URL: https://example.com/product
■ ターゲティング: [Nested Object]
  ■ 年齢: 20-30
  ■ 性別: 全て"""
    
    print(before_output)
    
    print("\n🟢 After (Flattened Structure):")
    after_output = """Meta
■ キャンペーン名: テストキャンペーン
■ フィード広告・ストーリー広告 - 見出し: 新商品登場！
■ フィード広告・ストーリー広告 - テキスト: 革新的な商品をお試しください
■ フィード広告・ストーリー広告 - URL: https://example.com/product
■ ターゲティング - 年齢: 20-30
■ ターゲティング - 性別: 全て"""
    
    print(after_output)
    
    print("\n✨ Benefits of Flattening:")
    print("• Consistent ■ key: value format throughout")
    print("• No nested indentation or complex structures")
    print("• Clear hierarchical field names with ' - ' separator")
    print("• Easy to read and parse")
    print("• Works with any level of nesting")

def show_real_world_example():
    """Show a real-world Meta ads example."""
    
    print("\n\n🌐 Real-World Meta Ads Example")
    print("=" * 60)
    
    print("\nInput JSON data:")
    real_data = """{
  "キャンペーン名": "美容商品春キャンペーン",
  "キャンペーンの目的": "コンバージョン",
  "広告セット名": ["美容セット1", "美容セット2"],
  "フィード広告・ストーリー広告": {
    "見出し": "美しさを引き出す新商品",
    "テキスト": "革新的な美容商品で、あなたの美しさを最大限に引き出しましょう。今すぐお試しください！",
    "URL": "https://example.com/beauty-product",
    "CTA": "今すぐ購入"
  },
  "カルーセル広告": {
    "カード1": {
      "見出し": "美容商品A",
      "説明": "肌に優しい成分配合"
    },
    "カード2": {
      "見出し": "美容商品B", 
      "説明": "長時間持続効果"
    }
  },
  "ターゲティング": {
    "年齢": "20-35",
    "性別": "女性",
    "興味": ["美容", "スキンケア", "コスメ"],
    "地域": "日本全国"
  }
}"""
    
    print(real_data)
    
    print("\nFlattened preview output:")
    flattened_output = """Meta
■ キャンペーン名: 美容商品春キャンペーン
■ キャンペーンの目的: コンバージョン
■ 広告セット名: 美容セット1, 美容セット2
■ フィード広告・ストーリー広告 - 見出し: 美しさを引き出す新商品
■ フィード広告・ストーリー広告 - テキスト: 革新的な美容商品で、あなたの美しさを最大限に引き出しましょう。今すぐお試しください！
■ フィード広告・ストーリー広告 - URL: https://example.com/beauty-product
■ フィード広告・ストーリー広告 - CTA: 今すぐ購入
■ カルーセル広告 - カード1 - 見出し: 美容商品A
■ カルーセル広告 - カード1 - 説明: 肌に優しい成分配合
■ カルーセル広告 - カード2 - 見出し: 美容商品B
■ カルーセル広告 - カード2 - 説明: 長時間持続効果
■ ターゲティング - 年齢: 20-35
■ ターゲティング - 性別: 女性
■ ターゲティング - 興味: 美容, スキンケア, コスメ
■ ターゲティング - 地域: 日本全国"""
    
    print(flattened_output)

def main():
    """Run the complete demonstration."""
    
    demonstrate_dict_flattening()
    show_before_after_comparison()
    show_real_world_example()
    
    print("\n\n🎉 Dictionary Flattening Complete!")
    print("=" * 60)
    print("✅ Key Features:")
    print("• Flattens any dictionary value into ■ key: value format")
    print("• Handles unlimited nesting levels")
    print("• Supports mixed data types (strings, numbers, lists, dicts)")
    print("• Uses ' - ' separator for hierarchical field names")
    print("• Maintains consistent formatting across all platforms")
    print("• Works automatically with any platform's JSON schema")

if __name__ == "__main__":
    main()

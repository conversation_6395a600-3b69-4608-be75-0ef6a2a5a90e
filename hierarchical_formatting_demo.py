#!/usr/bin/env python3
"""
Demonstration of hierarchical formatting in the PreviewTool.

This script shows how complex nested structures are formatted into
readable hierarchical text with proper indentation and alignment.
"""

def show_complex_data_example():
    """Show example of complex nested data structure."""
    
    print("🔧 Hierarchical Formatting Demo")
    print("=" * 60)
    
    print("\n📋 Input: Complex JSON Data Structure")
    print("-" * 40)
    
    complex_json = """{
  "キャンペーン名": "美容商品総合キャンペーン",
  "基本設定": {
    "予算": 100000,
    "期間": "30日間",
    "配信時間": ["9:00-12:00", "18:00-22:00"]
  },
  "ターゲティング": {
    "デモグラフィック": {
      "年齢層": ["20-25", "26-30", "31-35"],
      "性別": "女性",
      "詳細設定": {
        "収入レベル": "中間層以上",
        "教育レベル": ["大学卒", "大学院卒"]
      }
    },
    "興味関心": [
      {
        "カテゴリ": "美容",
        "サブカテゴリ": ["スキンケア", "メイクアップ"],
        "重要度": "高"
      },
      {
        "カテゴリ": "健康",
        "サブカテゴリ": ["フィットネス", "栄養"],
        "重要度": "中"
      }
    ]
  },
  "広告クリエイティブ": [
    {
      "タイプ": "フィード広告",
      "コンテンツ": {
        "見出し": "美しさを引き出す新商品",
        "説明文": "革新的な美容商品で、あなたの美しさを最大限に引き出します",
        "CTA": "今すぐ購入"
      }
    }
  ]
}"""
    
    print(complex_json)

def show_hierarchical_output():
    """Show the hierarchical formatted output."""
    
    print("\n📋 Output: Hierarchical Text Format")
    print("-" * 40)
    
    hierarchical_output = """Meta
■ キャンペーン名: 美容商品総合キャンペーン
■ 基本設定 - 予算: 100000
■ 基本設定 - 期間: 30日間
■ 基本設定 - 配信時間: 9:00-12:00, 18:00-22:00
■ ターゲティング - デモグラフィック - 年齢層: 20-25, 26-30, 31-35
■ ターゲティング - デモグラフィック - 性別: 女性
■ ターゲティング - デモグラフィック - 詳細設定 - 収入レベル: 中間層以上
■ ターゲティング - デモグラフィック - 詳細設定 - 教育レベル: 大学卒, 大学院卒
■ ターゲティング - 興味関心: [List with 2 items]
■ ターゲティング - 興味関心:   Item 1:
■ ターゲティング - 興味関心:     カテゴリ: 美容
■ ターゲティング - 興味関心:     サブカテゴリ: スキンケア, メイクアップ
■ ターゲティング - 興味関心:     重要度: 高
■ ターゲティング - 興味関心:   Item 2:
■ ターゲティング - 興味関心:     カテゴリ: 健康
■ ターゲティング - 興味関心:     サブカテゴリ: フィットネス, 栄養
■ ターゲティング - 興味関心:     重要度: 中
■ 広告クリエイティブ: [List with 1 items]
■ 広告クリエイティブ:   Item 1:
■ 広告クリエイティブ:     タイプ: フィード広告
■ 広告クリエイティブ:     コンテンツ: [Object with 3 properties]
■ 広告クリエイティブ:       見出し: 美しさを引き出す新商品
■ 広告クリエイティブ:       説明文: 革新的な美容商品で、あなたの美しさを最大限に引き出します
■ 広告クリエイティブ:       CTA: 今すぐ購入"""
    
    print(hierarchical_output)

def show_formatting_features():
    """Show the key features of hierarchical formatting."""
    
    print("\n✨ Hierarchical Formatting Features")
    print("-" * 40)
    
    features = [
        "🔹 Proper Indentation: 2 spaces per nesting level",
        "🔹 Clear Hierarchy: Parent-child relationships are obvious",
        "🔹 Consistent Format: All items use ■ key: value structure",
        "🔹 List Indicators: Shows [List with X items] for complex lists",
        "🔹 Object Indicators: Shows [Object with X properties] for nested objects",
        "🔹 Item Numbering: Clear Item 1, Item 2 for list elements",
        "🔹 Mixed Type Support: Handles strings, numbers, booleans, lists, dicts",
        "🔹 Unlimited Depth: Supports any level of nesting",
        "🔹 Aligned Structure: Items at same level are properly aligned",
        "🔹 Readable Format: Easy to scan and understand"
    ]
    
    for feature in features:
        print(f"  {feature}")

def show_data_type_examples():
    """Show examples of different data type formatting."""
    
    print("\n📊 Data Type Formatting Examples")
    print("-" * 40)
    
    examples = [
        ("Simple String", "■ 商品名: 美容クリーム"),
        ("Number", "■ 価格: 2980"),
        ("Boolean", "■ 有効: True"),
        ("Simple List", "■ カテゴリ: 美容, スキンケア, 化粧品"),
        ("Complex List", """■ 商品リスト: [List with 2 items]
■ 商品リスト:   Item 1:
■ 商品リスト:     名前: 商品A
■ 商品リスト:     価格: 1980
■ 商品リスト:   Item 2:
■ 商品リスト:     名前: 商品B
■ 商品リスト:     価格: 2980"""),
        ("Nested Dictionary", """■ 設定 - 基本 - 予算: 50000
■ 設定 - 基本 - 期間: 30日
■ 設定 - 詳細 - 配信時間: 9:00-18:00"""),
        ("Mixed Structure", """■ 複雑データ: [Object with 3 properties]
■ 複雑データ:   文字列: テスト値
■ 複雑データ:   リスト: 項目1, 項目2, 項目3
■ 複雑データ:   ネスト: [Object with 2 properties]
■ 複雑データ:     サブ値1: 値A
■ 複雑データ:     サブ値2: 値B""")
    ]
    
    for data_type, example in examples:
        print(f"\n🔸 {data_type}:")
        for line in example.split('\n'):
            print(f"  {line}")

def show_comparison_with_old_format():
    """Show comparison between old flat format and new hierarchical format."""
    
    print("\n📊 Format Comparison")
    print("=" * 60)
    
    print("\n🔴 Old Format (Flat Structure):")
    old_format = """Meta
■ キャンペーン名: 美容商品キャンペーン
■ フィード広告・ストーリー広告 - 見出し: 新商品登場
■ フィード広告・ストーリー広告 - テキスト: 革新的な商品です
■ カルーセル広告 - カード1 - 見出し: 商品A
■ カルーセル広告 - カード1 - 説明: 商品Aの説明
■ ターゲティング - 年齢: 20-30
■ ターゲティング - 性別: 女性"""
    
    print(old_format)
    
    print("\n🟢 New Format (Hierarchical Structure):")
    new_format = """Meta
■ キャンペーン名: 美容商品キャンペーン
■ フィード広告・ストーリー広告: [Object with 2 properties]
■ フィード広告・ストーリー広告:   見出し: 新商品登場
■ フィード広告・ストーリー広告:   テキスト: 革新的な商品です
■ カルーセル広告: [List with 1 items]
■ カルーセル広告:   Item 1:
■ カルーセル広告:     見出し: 商品A
■ カルーセル広告:     説明: 商品Aの説明
■ ターゲティング: [Object with 2 properties]
■ ターゲティング:   年齢: 20-30
■ ターゲティング:   性別: 女性"""
    
    print(new_format)
    
    print("\n✨ Improvements:")
    improvements = [
        "• Clear visual hierarchy with proper indentation",
        "• Object and list indicators show data structure",
        "• Easier to understand parent-child relationships",
        "• Better alignment for items at the same level",
        "• More intuitive reading flow",
        "• Supports unlimited nesting complexity"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")

def main():
    """Run the complete hierarchical formatting demonstration."""
    
    show_complex_data_example()
    show_hierarchical_output()
    show_formatting_features()
    show_data_type_examples()
    show_comparison_with_old_format()
    
    print("\n\n🎉 Hierarchical Formatting Complete!")
    print("=" * 60)
    print("✅ The new formatting system provides:")
    print("• Clear visual hierarchy with proper indentation")
    print("• Support for any complexity of nested structures")
    print("• Consistent ■ key: value format throughout")
    print("• Easy-to-read structure indicators")
    print("• Proper alignment for better readability")
    print("• Universal support for all data types")

if __name__ == "__main__":
    main()

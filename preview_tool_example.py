#!/usr/bin/env python3
"""
Example demonstrating the updated PreviewTool with LLM extraction.

This example shows how the preview tool now extracts real data from conversation
history and formats it in the requested format.
"""

import asyncio
import json

async def demonstrate_preview_tool_flow():
    """Demonstrate the complete preview tool flow."""
    
    print("🎯 Preview Tool with LLM Extraction Demo")
    print("=" * 50)
    
    # Step 1: User conversation with advertising content
    print("\n1️⃣ User Conversation:")
    conversation = [
        "User: 新しい美容商品の広告を作成してください。ターゲットは20代女性で、東京と大阪で配信したいです。",
        "Assistant: 美容商品の広告を作成いたします。20代女性をターゲットとした「美容商品春キャンペーン」として、東京・大阪エリアで配信する広告を準備しましょう。",
        "User: プレビューを表示してください"
    ]
    
    for message in conversation:
        print(f"  {message}")
        await asyncio.sleep(0.5)
    
    # Step 2: LLM extraction process
    print(f"\n2️⃣ LLM Data Extraction:")
    print("  📤 Sending conversation to LLM for data extraction...")
    await asyncio.sleep(1)
    
    # Simulate extracted data
    extracted_data = {
        "data": {
            "キャンペーン": "美容商品春キャンペーン",
            "グループ": "20代女性向け美容商品",
            "配信条件／配信ポイント": "20-30歳女性、東京・大阪エリア、美容・健康に興味のあるユーザー",
            "年齢": "20-30",
            "性別": "女性",
            "エリア": "東京・大阪",
            "見出し": "新商品登場！美しさを引き出す",
            "説明文": "革新的な美容商品で、あなたの美しさを最大限に引き出します。今すぐお試しください！"
        },
        "missing_fields": []
    }
    
    print("  📥 LLM extracted the following data:")
    for field, value in extracted_data["data"].items():
        print(f"    • {field}: {value}")
    
    # Step 3: Format into preview
    print(f"\n3️⃣ Formatting Preview Content:")
    print("  🔄 Converting extracted data to preview format...")
    await asyncio.sleep(1)
    
    # Simulate the formatted preview
    preview_content = """LINE
■ キャンペーン名: 美容商品春キャンペーン [15–30 chars]
■ ターゲット: 20代女性向け美容商品 [10–25 chars]
■ 配信条件: 20-30歳女性、東京・大阪エリア、美容・健康に興味のあるユーザー [50–100 chars]
■ 年齢層: 20-30
■ 性別: 女性
■ 地域: 東京・大阪"""
    
    # Step 4: Stream to user
    print(f"\n4️⃣ Streaming to User:")
    print("  🌊 Streaming preview content...")
    await asyncio.sleep(0.5)
    
    print("\n" + "=" * 50)
    lines = preview_content.split('\n')
    for line in lines:
        print(line)
        await asyncio.sleep(0.3)  # Simulate streaming delay
    print("=" * 50)
    
    # Step 5: No follow-up response
    print(f"\n5️⃣ Flow Completion:")
    print("  ✅ Preview streamed successfully")
    print("  🚫 No follow-up response generated")
    print("  ✨ User sees clean, formatted preview")

async def show_comparison():
    """Show comparison between old and new approach."""
    
    print(f"\n\n📊 Comparison: Old vs New Approach")
    print("=" * 60)
    
    print(f"\n🔴 Old Approach (Schema-based):")
    old_format = """LINE
■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]
■ ターゲット: [商品またはオーディエンス, 10–25 chars]
■ 配信条件: [年齢、性別、興味、地域など, 50–100 chars]
■ 年齢層: [18–65 or specific range]
■ 性別: [全て / 男性 / 女性]
■ 地域: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]"""
    
    print(old_format)
    
    print(f"\n🟢 New Approach (LLM-extracted):")
    new_format = """LINE
■ キャンペーン名: 美容商品春キャンペーン [15–30 chars]
■ ターゲット: 20代女性向け美容商品 [10–25 chars]
■ 配信条件: 20-30歳女性、東京・大阪エリア、美容・健康に興味のあるユーザー [50–100 chars]
■ 年齢層: 20-30
■ 性別: 女性
■ 地域: 東京・大阪"""
    
    print(new_format)
    
    print(f"\n✨ Key Improvements:")
    print("  • Shows actual extracted content instead of placeholders")
    print("  • Uses LLM to understand conversation context")
    print("  • Provides real preview of what will be in Excel file")
    print("  • Maintains character limit information for reference")
    print("  • Falls back to placeholders when data is missing")

async def show_technical_details():
    """Show technical implementation details."""
    
    print(f"\n\n🔧 Technical Implementation")
    print("=" * 60)
    
    print(f"\n📋 Process Flow:")
    steps = [
        "1. User requests preview",
        "2. Tool validates advertising content exists",
        "3. Tool loads platform schema (LINE, Meta, etc.)",
        "4. Tool generates extraction prompt using schema",
        "5. LLM extracts data from conversation history",
        "6. Tool formats extracted data into preview format",
        "7. Content streams to user in real-time",
        "8. No follow-up response is generated"
    ]
    
    for step in steps:
        print(f"  {step}")
        await asyncio.sleep(0.2)
    
    print(f"\n🛠️ Key Methods:")
    methods = [
        "_extract_campaign_data(): Uses LLM to extract data from conversation",
        "_format_preview_content(): Formats extracted data into preview format",
        "_create_standardized_fields_with_data(): Maps platform fields to standard format",
        "_create_stream_generator(): Provides streaming functionality"
    ]
    
    for method in methods:
        print(f"  • {method}")
    
    print(f"\n🌐 Platform Support:")
    platforms = [
        "LINE Ads", "Meta (Instagram/Facebook)", "YouTube Ads",
        "Google Search Ads", "Google Display Ads", "Google Demand Gen Ads",
        "P-Max", "Ad Extensions"
    ]
    
    for platform in platforms:
        print(f"  • {platform}")

async def main():
    """Run the complete demonstration."""
    
    await demonstrate_preview_tool_flow()
    await show_comparison()
    await show_technical_details()
    
    print(f"\n\n🎉 Preview Tool Update Complete!")
    print("=" * 60)
    print("✅ The preview tool now extracts real data from conversations")
    print("✅ Shows actual content instead of just placeholders")
    print("✅ Maintains streaming functionality")
    print("✅ Prevents unwanted follow-up responses")
    print("✅ Supports all advertising platforms")

if __name__ == "__main__":
    asyncio.run(main())

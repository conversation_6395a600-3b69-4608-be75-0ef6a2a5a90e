#!/usr/bin/env python3
"""
Demonstration of recursive field extraction logic.

This script shows how the recursive extraction works to extract all
nested content until reaching actual string values.
"""

def demonstrate_recursive_logic():
    """Demonstrate the recursive extraction logic."""
    
    print("🔧 Recursive Field Extraction Demo")
    print("=" * 60)
    
    print("\n📋 Input: Complex Nested JSON Structure")
    print("-" * 40)
    
    complex_data = {
        "キャンペーン名": "美容商品キャンペーン",
        "基本設定": {
            "予算": 100000,
            "期間": "30日間",
            "配信時間": ["9:00-12:00", "18:00-22:00"]
        },
        "ターゲティング": {
            "デモグラフィック": {
                "年齢": "20-30",
                "性別": "女性",
                "詳細": {
                    "収入": "中間層以上",
                    "教育": ["大学卒", "大学院卒"]
                }
            },
            "興味関心": [
                {
                    "カテゴリ": "美容",
                    "重要度": "高"
                },
                {
                    "カテゴリ": "健康",
                    "重要度": "中"
                }
            ]
        },
        "広告リスト": [
            {
                "タイプ": "フィード広告",
                "コンテンツ": {
                    "見出し": "美しさを引き出す",
                    "説明": "革新的な美容商品"
                }
            }
        ]
    }
    
    print("Input JSON:")
    import json
    print(json.dumps(complex_data, indent=2, ensure_ascii=False))

def show_recursive_extraction_output():
    """Show the output of recursive extraction."""
    
    print("\n📋 Output: Recursively Extracted Fields")
    print("-" * 40)
    
    extracted_output = """■ キャンペーン名: 美容商品キャンペーン
■ 基本設定 - 予算: 100000
■ 基本設定 - 期間: 30日間
■ 基本設定 - 配信時間: 9:00-12:00, 18:00-22:00
■ ターゲティング - デモグラフィック - 年齢: 20-30
■ ターゲティング - デモグラフィック - 性別: 女性
■ ターゲティング - デモグラフィック - 詳細 - 収入: 中間層以上
■ ターゲティング - デモグラフィック - 詳細 - 教育: 大学卒, 大学院卒
■ ターゲティング - 興味関心 - Item 1 - カテゴリ: 美容
■ ターゲティング - 興味関心 - Item 1 - 重要度: 高
■ ターゲティング - 興味関心 - Item 2 - カテゴリ: 健康
■ ターゲティング - 興味関心 - Item 2 - 重要度: 中
■ 広告リスト - Item 1 - タイプ: フィード広告
■ 広告リスト - Item 1 - コンテンツ - 見出し: 美しさを引き出す
■ 広告リスト - Item 1 - コンテンツ - 説明: 革新的な美容商品"""
    
    print(extracted_output)

def show_recursive_algorithm():
    """Show the recursive algorithm logic."""
    
    print("\n🔄 Recursive Algorithm Logic")
    print("-" * 40)
    
    algorithm_steps = [
        "1. Start with field name and value",
        "2. Check value type:",
        "   • If string/number/boolean → Extract as final value",
        "   • If dictionary → Recurse into each key-value pair",
        "   • If list → Check list content type:",
        "     - Simple values → Join as comma-separated",
        "     - Complex objects → Recurse into each item",
        "3. For dictionaries:",
        "   • Create new field name: parent - child",
        "   • Recursively process each child value",
        "4. For lists with objects:",
        "   • Create item field name: parent - Item N",
        "   • Recursively process each item",
        "5. Continue until all values are strings/primitives"
    ]
    
    for step in algorithm_steps:
        print(f"  {step}")

def show_extraction_examples():
    """Show specific extraction examples."""
    
    print("\n📊 Extraction Examples by Data Type")
    print("-" * 40)
    
    examples = [
        {
            "type": "Simple String",
            "input": '"美容商品キャンペーン"',
            "output": "■ キャンペーン名: 美容商品キャンペーン"
        },
        {
            "type": "Simple Dictionary",
            "input": '{"予算": 100000, "期間": "30日間"}',
            "output": """■ 基本設定 - 予算: 100000
■ 基本設定 - 期間: 30日間"""
        },
        {
            "type": "Simple List",
            "input": '["項目1", "項目2", "項目3"]',
            "output": "■ リスト: 項目1, 項目2, 項目3"
        },
        {
            "type": "Nested Dictionary",
            "input": '{"詳細": {"収入": "中間層", "教育": "大学卒"}}',
            "output": """■ 設定 - 詳細 - 収入: 中間層
■ 設定 - 詳細 - 教育: 大学卒"""
        },
        {
            "type": "List with Objects",
            "input": '[{"名前": "商品A", "価格": 1000}, {"名前": "商品B", "価格": 2000}]',
            "output": """■ 商品 - Item 1 - 名前: 商品A
■ 商品 - Item 1 - 価格: 1000
■ 商品 - Item 2 - 名前: 商品B
■ 商品 - Item 2 - 価格: 2000"""
        }
    ]
    
    for example in examples:
        print(f"\n🔸 {example['type']}:")
        print(f"  Input:  {example['input']}")
        print(f"  Output:")
        for line in example['output'].split('\n'):
            print(f"    {line}")

def show_benefits():
    """Show the benefits of recursive extraction."""
    
    print("\n✨ Benefits of Recursive Extraction")
    print("-" * 40)
    
    benefits = [
        "🔹 Complete Information Extraction:",
        "  • No nested content is lost",
        "  • All values are extracted to string level",
        "  • Preserves hierarchical relationships",
        "",
        "🔹 Consistent Output Format:",
        "  • All fields use ■ key: value structure",
        "  • Clear parent-child naming with ' - ' separator",
        "  • Uniform handling across all data types",
        "",
        "🔹 Optimized for Readability:",
        "  • Simple lists become comma-separated values",
        "  • Complex structures maintain clear hierarchy",
        "  • Easy to scan and understand",
        "",
        "🔹 Unlimited Depth Support:",
        "  • Handles any level of nesting",
        "  • Scales to complex data structures",
        "  • No artificial limitations",
        "",
        "🔹 Type-Agnostic Processing:",
        "  • Works with any JSON structure",
        "  • Handles mixed data types",
        "  • Adapts to platform-specific schemas"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")

def show_comparison():
    """Show comparison with previous approach."""
    
    print("\n📊 Comparison: Previous vs Recursive Approach")
    print("=" * 60)
    
    print("\n🔴 Previous Approach (Limited Extraction):")
    previous_output = """■ ターゲティング: [Object with 2 properties]
■ ターゲティング - デモグラフィック: [Object with 3 properties]
■ ターゲティング - 興味関心: [List with 2 items]"""
    
    print(previous_output)
    
    print("\n🟢 Recursive Approach (Complete Extraction):")
    recursive_output = """■ ターゲティング - デモグラフィック - 年齢: 20-30
■ ターゲティング - デモグラフィック - 性別: 女性
■ ターゲティング - デモグラフィック - 詳細 - 収入: 中間層以上
■ ターゲティング - デモグラフィック - 詳細 - 教育: 大学卒, 大学院卒
■ ターゲティング - 興味関心 - Item 1 - カテゴリ: 美容
■ ターゲティング - 興味関心 - Item 1 - 重要度: 高
■ ターゲティング - 興味関心 - Item 2 - カテゴリ: 健康
■ ターゲティング - 興味関心 - Item 2 - 重要度: 中"""
    
    print(recursive_output)
    
    print("\n✨ Key Improvements:")
    improvements = [
        "• All actual values are extracted and displayed",
        "• No information is hidden behind object/list indicators",
        "• Users can see complete content without guessing",
        "• Perfect match with Excel file content",
        "• Ready for direct use and review"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")

def main():
    """Run the complete recursive extraction demonstration."""
    
    demonstrate_recursive_logic()
    show_recursive_extraction_output()
    show_recursive_algorithm()
    show_extraction_examples()
    show_benefits()
    show_comparison()
    
    print("\n\n🎉 Recursive Extraction Complete!")
    print("=" * 60)
    print("✅ The recursive extraction ensures:")
    print("• Complete information extraction from any nested structure")
    print("• All values are processed until reaching actual content")
    print("• Consistent ■ key: value format throughout")
    print("• Clear hierarchical naming with ' - ' separators")
    print("• Optimal handling of both simple and complex data types")
    print("• Perfect alignment with user expectations")

if __name__ == "__main__":
    main()

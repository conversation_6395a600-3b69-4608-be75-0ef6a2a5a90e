#!/usr/bin/env python3
"""
Example demonstrating the streaming integration of the PreviewTool.

This example shows how the preview tool's streaming response flows through
the system to reach the user.
"""

import asyncio
import json
from typing import AsyncGenerator

async def simulate_preview_tool_streaming() -> dict:
    """Simulate the preview tool returning a streaming response."""
    
    # This is what the preview tool returns
    content = """LINE
■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]
■ ターゲット: [商品またはオーディエンス, 10–25 chars]
■ 配信条件: [年齢、性別、興味、地域など, 50–100 chars]
■ 年齢層: [18–65 or specific range]
■ 性別: [全て / 男性 / 女性]
■ 地域: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]"""
    
    async def stream_generator():
        """Generator that streams the content line by line."""
        lines = content.split('\n')
        for line in lines:
            await asyncio.sleep(0.2)  # Simulate streaming delay
            yield line + '\n'
    
    return {
        "type": "stream_response",
        "content": content,
        "platform": "LINE Ads",
        "extraction_status": "success",
        "stream_generator": stream_generator()
    }

async def simulate_stream_processor_handling(tool_result: dict) -> AsyncGenerator[str, None]:
    """Simulate how the stream processor handles the streaming tool response."""
    
    print("🔧 Stream Processor: Tool executed, checking result type...")
    
    # Check if this is a streaming response from preview tool
    if isinstance(tool_result, dict) and tool_result.get("type") == "stream_response":
        print("🌊 Stream Processor: Detected streaming response, starting stream...")
        
        # Add newline before streaming content
        yield "\n"
        
        # Stream the content directly to user
        if "stream_generator" in tool_result:
            async for chunk in tool_result["stream_generator"]:
                print(f"🌊 Streaming chunk: {repr(chunk)}")
                yield chunk
        else:
            # Fallback to static content if no generator
            yield tool_result.get("content", "")
        
        # Add newline after streaming content
        yield "\n"
        
        print("✅ Stream Processor: Streaming completed")
    else:
        # Handle regular tool response
        yield json.dumps(tool_result, ensure_ascii=False)

async def simulate_chatbot_controller_streaming(stream_processor_output: AsyncGenerator[str, None]):
    """Simulate how the chatbot controller handles the streaming response."""
    
    print("📡 Chatbot Controller: Starting response stream...")
    
    # Simulate the controller's streaming response format
    yield 'message: "session_id": "test-session-123" \n'
    
    response_content = ""
    async for chunk in stream_processor_output:
        response_content += chunk
        
        # Format as server-sent event
        res = {"response": chunk}
        result = {"code": "BE0000", "message": "success", "data": res}
        yield f"data: {json.dumps(result, ensure_ascii=False)}\n"
    
    yield "message: Done"
    
    print(f"📡 Chatbot Controller: Stream completed. Total content length: {len(response_content)}")

async def demonstrate_full_streaming_flow():
    """Demonstrate the complete streaming flow from tool to user."""
    
    print("🎯 Demonstrating Full Streaming Flow")
    print("=" * 50)
    
    # Step 1: Preview tool generates streaming response
    print("\n1️⃣ Preview Tool: Generating streaming response...")
    tool_result = await simulate_preview_tool_streaming()
    print(f"   ✅ Generated response with type: {tool_result['type']}")
    
    # Step 2: Stream processor handles the streaming response
    print("\n2️⃣ Stream Processor: Processing tool response...")
    stream_processor_output = simulate_stream_processor_handling(tool_result)
    
    # Step 3: Chatbot controller formats for client
    print("\n3️⃣ Chatbot Controller: Formatting for client...")
    controller_output = simulate_chatbot_controller_streaming(stream_processor_output)
    
    # Step 4: Simulate what the client receives
    print("\n4️⃣ Client Receives:")
    print("-" * 30)
    
    async for event in controller_output:
        print(event, end='')
        await asyncio.sleep(0.1)  # Simulate network delay
    
    print("\n" + "-" * 30)
    print("✅ Complete streaming flow demonstrated!")

async def demonstrate_user_experience():
    """Demonstrate what the user actually sees."""
    
    print("\n\n👤 User Experience Simulation")
    print("=" * 50)
    
    print("User: プレビューを表示してください")
    print("Assistant: プレビューを生成しています...")
    
    # Simulate the streaming preview content
    content = """LINE
■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]
■ ターゲット: [商品またはオーディエンス, 10–25 chars]
■ 配信条件: [年齢、性別、興味、地域など, 50–100 chars]
■ 年齢層: [18–65 or specific range]
■ 性別: [全て / 男性 / 女性]
■ 地域: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]"""
    
    print("\n📋 Preview Content (streaming to user):")
    lines = content.split('\n')
    for line in lines:
        print(line)
        await asyncio.sleep(0.3)  # Simulate streaming delay
    
    print("\nAssistant: プレビューが生成されました。内容をご確認ください。")
    print("\n✅ User sees the complete streaming preview!")

async def main():
    """Run the streaming integration demonstration."""
    
    print("🚀 Preview Tool Streaming Integration Demo")
    print("=" * 60)
    
    await demonstrate_full_streaming_flow()
    await demonstrate_user_experience()
    
    print("\n🎉 Demo completed successfully!")
    print("\nKey Benefits:")
    print("• Real-time preview generation")
    print("• Smooth streaming user experience")
    print("• Immediate feedback to users")
    print("• No waiting for complete response")

if __name__ == "__main__":
    asyncio.run(main())

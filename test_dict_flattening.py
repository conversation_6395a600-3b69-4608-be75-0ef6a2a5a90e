#!/usr/bin/env python3
"""
Test script for dictionary value flattening in the PreviewTool.

This script tests that dictionary values are properly flattened into
individual ■ key: value lines instead of nested structures.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_dict_flattening():
    """Test that dictionary values are flattened into ■ key: value format."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing Dictionary Value Flattening")
        print("=" * 50)
        
        # Mock LLM response with dictionary values
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン名": "辞書テストキャンペーン",
                "フィード広告・ストーリー広告": {
                    "見出し": "新商品登場！",
                    "テキスト": "革新的な商品をお試しください",
                    "URL": "https://example.com/product",
                    "CTA": "今すぐ購入"
                },
                "カルーセル広告": {
                    "カード1": {
                        "見出し": "商品A",
                        "説明": "商品Aの説明"
                    },
                    "カード2": {
                        "見出し": "商品B",
                        "説明": "商品Bの説明"
                    }
                },
                "ターゲティング": {
                    "年齢": "20-30",
                    "性別": "全て",
                    "興味": ["美容", "健康", "ライフスタイル"],
                    "地域": "日本全国"
                }
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "辞書形式のデータを含む広告を作成してください。"},
                {"role": "assistant", "content": "辞書形式のデータを含む広告を作成いたします。フィード広告、カルーセル広告、ターゲティング情報などを準備しましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test Meta platform (which commonly has dictionary values)
        result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Dictionary flattening test successful!")
        print("📋 Flattened Dictionary Preview:")
        print("=" * 60)
        print(result['content'])
        print("=" * 60)
        
        # Analyze the flattened output
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        print(f"\n🔍 Analysis:")
        print(f"Total field lines: {len(field_lines)}")
        
        # Check for flattened dictionary fields
        flattened_fields = []
        regular_fields = []
        
        for line in field_lines:
            if " - " in line:
                flattened_fields.append(line)
            else:
                regular_fields.append(line)
        
        print(f"Regular fields: {len(regular_fields)}")
        print(f"Flattened dictionary fields: {len(flattened_fields)}")
        
        print(f"\n📝 Regular Fields:")
        for field in regular_fields[:3]:  # Show first 3
            field_name = field.split(":")[0].replace("■ ", "") if ":" in field else field
            print(f"  • {field_name}")
        
        print(f"\n📂 Flattened Dictionary Fields:")
        for field in flattened_fields[:8]:  # Show first 8
            field_name = field.split(":")[0].replace("■ ", "") if ":" in field else field
            field_value = field.split(":", 1)[1].strip() if ":" in field else ""
            print(f"  • {field_name}: {field_value}")
        
        if len(flattened_fields) > 8:
            print(f"  ... and {len(flattened_fields) - 8} more flattened fields")
        
        # Verify specific flattened structures
        expected_patterns = [
            "フィード広告・ストーリー広告 - 見出し",
            "フィード広告・ストーリー広告 - テキスト", 
            "フィード広告・ストーリー広告 - URL",
            "カルーセル広告 - カード1 - 見出し",
            "カルーセル広告 - カード2 - 見出し",
            "ターゲティング - 年齢",
            "ターゲティング - 性別"
        ]
        
        found_patterns = []
        for pattern in expected_patterns:
            for line in field_lines:
                if pattern in line:
                    found_patterns.append(pattern)
                    break
        
        print(f"\n✅ Verification:")
        print(f"Expected flattened patterns: {len(expected_patterns)}")
        print(f"Found flattened patterns: {len(found_patterns)}")
        
        for pattern in found_patterns:
            print(f"  ✅ {pattern}")
        
        missing_patterns = set(expected_patterns) - set(found_patterns)
        for pattern in missing_patterns:
            print(f"  ❌ Missing: {pattern}")
        
        return len(missing_patterns) == 0
        
    except Exception as e:
        print(f"❌ Dictionary flattening test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_nested_dict_flattening():
    """Test deeply nested dictionary flattening."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("\n🔧 Testing Nested Dictionary Flattening")
        print("=" * 50)
        
        # Mock LLM response with deeply nested dictionaries
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン名": "ネスト辞書テスト",
                "広告設定": {
                    "基本設定": {
                        "予算": "10000円",
                        "期間": "30日間"
                    },
                    "ターゲティング": {
                        "デモグラフィック": {
                            "年齢": "20-30",
                            "性別": "全て"
                        },
                        "興味関心": {
                            "カテゴリ": ["美容", "健康"],
                            "キーワード": ["新商品", "おすすめ"]
                        }
                    }
                }
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "ネストした辞書データを含む広告を作成してください。"},
                {"role": "assistant", "content": "ネストした辞書データを含む広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test with nested dictionaries
        result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Nested dictionary flattening successful!")
        print("📋 Nested Dictionary Preview:")
        print("=" * 60)
        print(result['content'])
        print("=" * 60)
        
        # Check for deeply nested flattening
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        deeply_nested_fields = [line for line in field_lines if line.count(" - ") >= 2]
        
        print(f"\n🔍 Nested Analysis:")
        print(f"Total fields: {len(field_lines)}")
        print(f"Deeply nested fields (2+ levels): {len(deeply_nested_fields)}")
        
        print(f"\n📂 Deeply Nested Fields:")
        for field in deeply_nested_fields:
            field_name = field.split(":")[0].replace("■ ", "") if ":" in field else field
            field_value = field.split(":", 1)[1].strip() if ":" in field else ""
            print(f"  • {field_name}: {field_value}")
        
        # Verify specific nested patterns
        expected_nested = [
            "広告設定 - 基本設定 - 予算",
            "広告設定 - 基本設定 - 期間",
            "広告設定 - ターゲティング - デモグラフィック - 年齢",
            "広告設定 - ターゲティング - デモグラフィック - 性別"
        ]
        
        found_nested = []
        for pattern in expected_nested:
            for line in field_lines:
                if pattern in line:
                    found_nested.append(pattern)
                    break
        
        print(f"\n✅ Nested Verification:")
        for pattern in found_nested:
            print(f"  ✅ {pattern}")
        
        missing_nested = set(expected_nested) - set(found_nested)
        for pattern in missing_nested:
            print(f"  ❌ Missing: {pattern}")
        
        return len(missing_nested) == 0
        
    except Exception as e:
        print(f"❌ Nested dictionary test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_mixed_data_types():
    """Test flattening with mixed data types in dictionaries."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Test the flattening method directly
        tool = PreviewTool(Mock(), Mock())
        
        print("\n🔧 Testing Mixed Data Types in Dictionaries")
        print("=" * 50)
        
        # Test data with mixed types
        test_dict = {
            "文字列": "テスト文字列",
            "数値": 12345,
            "リスト": ["項目1", "項目2", "項目3"],
            "ネスト辞書": {
                "サブ文字列": "サブテスト",
                "サブリスト": ["サブ項目1", "サブ項目2"]
            }
        }
        
        # Test the flattening method
        flattened = tool._flatten_dict_value("テストフィールド", test_dict)
        
        print("📋 Flattened Mixed Types:")
        for key, value in flattened.items():
            print(f"■ {key}: {value}")
        
        # Verify expected keys
        expected_keys = [
            "テストフィールド - 文字列",
            "テストフィールド - 数値", 
            "テストフィールド - リスト",
            "テストフィールド - ネスト辞書 - サブ文字列",
            "テストフィールド - ネスト辞書 - サブリスト"
        ]
        
        print(f"\n✅ Type Verification:")
        for key in expected_keys:
            if key in flattened:
                print(f"  ✅ {key}: {flattened[key]}")
            else:
                print(f"  ❌ Missing: {key}")
        
        return all(key in flattened for key in expected_keys)
        
    except Exception as e:
        print(f"❌ Mixed data types test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all dictionary flattening tests."""
    print("🧪 Testing Dictionary Value Flattening")
    print("=" * 70)
    
    # Test 1: Basic dictionary flattening
    basic_success = await test_dict_flattening()
    
    # Test 2: Nested dictionary flattening
    nested_success = await test_nested_dict_flattening()
    
    # Test 3: Mixed data types
    mixed_success = await test_mixed_data_types()
    
    print(f"\n📊 Test Results:")
    print(f"Basic Dictionary Flattening: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Nested Dictionary Flattening: {'✅ PASS' if nested_success else '❌ FAIL'}")
    print(f"Mixed Data Types: {'✅ PASS' if mixed_success else '❌ FAIL'}")
    
    if basic_success and nested_success and mixed_success:
        print("\n🎉 All dictionary flattening tests passed!")
        print("\n✅ Dictionary flattening features:")
        print("• Flattens dictionary values into ■ key: value format")
        print("• Handles nested dictionaries with multiple levels")
        print("• Supports mixed data types (strings, numbers, lists)")
        print("• Creates clear hierarchical field names with ' - ' separator")
        print("• Maintains proper formatting for all platforms")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to verify the exact output format of the PreviewTool.

This script tests that the output matches the requested format exactly.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_line_ads_format():
    """Test that LINE Ads format matches the requested output exactly."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Mock LLM response for LINE Ads
        line_response = Mock()
        line_response.choices = [Mock()]
        line_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "テスト商品キャンペーン",
                "グループ": "20代女性向けテスト商品",
                "配信条件／配信ポイント": "20-30歳女性、東京・大阪エリア、美容・健康に興味",
                "年齢": "20-30",
                "性別": "女性",
                "エリア": "",  # Empty to test the special case
                "見出し": "新商品登場！",
                "説明文": "革新的な美容商品で、あなたの美しさを引き出します。今すぐお試しください！"
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=line_response)
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
                {"role": "assistant", "content": "美容商品の広告を作成いたします。キャンペーン名、ターゲット、見出し、説明文などの広告コンテンツを準備しましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads
        print("🚀 Testing LINE Ads format...")
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("📋 LINE Ads Preview Output:")
        print(result['preview_content'])
        print()
        
        # Expected format verification
        expected_lines = [
            "LINE",
            "■ キャンペーン: [広告主 + 商品 + 目的, max 30 chars]",
            "■ グループ: [Single string representing group (e.g., 商品とオーディエンスをまとめた一文), max 50 chars]",
            "■ 配信条件／配信ポイント: [Single string summarizing targeting condition (e.g., 年齢・性別・地域などを含む1文), max 100 chars]",
            "■ 年齢: [18–65 or specific range]",
            "■ 性別: [全て / 男性 / 女性]",
            "■ エリア: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]",
            "■ 見出し: [Ad headline, max 20 chars]",
            "■ 説明文: [Ad description, max 75 chars]"
        ]
        
        actual_lines = result['preview_content'].split('\n')
        
        print("🔍 Format Verification:")
        for i, (expected, actual) in enumerate(zip(expected_lines, actual_lines)):
            if expected == actual:
                print(f"✅ Line {i+1}: MATCH")
            else:
                print(f"❌ Line {i+1}: MISMATCH")
                print(f"   Expected: {expected}")
                print(f"   Actual:   {actual}")
        
        print(f"\n📊 Total lines - Expected: {len(expected_lines)}, Actual: {len(actual_lines)}")
        
        # Test with area value provided
        print("\n🚀 Testing LINE Ads with area value...")
        line_response_with_area = Mock()
        line_response_with_area.choices = [Mock()]
        line_response_with_area.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "テスト商品キャンペーン",
                "グループ": "20代女性向けテスト商品", 
                "配信条件／配信ポイント": "20-30歳女性、東京・大阪エリア、美容・健康に興味",
                "年齢": "20-30",
                "性別": "女性",
                "エリア": "日本全国",  # Provided value
                "見出し": "新商品登場！",
                "説明文": "革新的な美容商品で、あなたの美しさを引き出します。今すぐお試しください！"
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion.return_value = line_response_with_area
        
        result_with_area = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("📋 LINE Ads Preview with Area:")
        area_lines = result_with_area['preview_content'].split('\n')
        for line in area_lines:
            if "エリア" in line:
                print(f"Area line: {line}")
                break
        
        print("\n🎉 Format verification completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_line_ads_format())

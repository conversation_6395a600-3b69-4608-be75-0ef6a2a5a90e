#!/usr/bin/env python3
"""
Test script for hierarchical formatting in the PreviewTool.

This script tests the new complex structure formatting that handles
dictionaries with lists, lists containing dictionaries, etc.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_complex_nested_structures():
    """Test formatting of complex nested structures."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing Complex Nested Structure Formatting")
        print("=" * 60)
        
        # Mock LLM response with very complex nested data
        complex_data = {
            "data": {
                "キャンペーン名": "複雑構造テストキャンペーン",
                "基本設定": {
                    "予算": 50000,
                    "期間": "30日間",
                    "配信時間": ["9:00-12:00", "18:00-22:00"]
                },
                "ターゲティング": {
                    "デモグラフィック": {
                        "年齢層": ["20-25", "26-30", "31-35"],
                        "性別": "全て",
                        "詳細設定": {
                            "収入レベル": "中間層以上",
                            "教育レベル": ["大学卒", "大学院卒"]
                        }
                    },
                    "興味関心": [
                        {
                            "カテゴリ": "美容",
                            "サブカテゴリ": ["スキンケア", "メイクアップ"],
                            "重要度": "高"
                        },
                        {
                            "カテゴリ": "健康",
                            "サブカテゴリ": ["フィットネス", "栄養"],
                            "重要度": "中"
                        }
                    ]
                },
                "広告クリエイティブ": [
                    {
                        "タイプ": "フィード広告",
                        "コンテンツ": {
                            "見出し": "美しさを引き出す新商品",
                            "説明文": "革新的な美容商品で、あなたの美しさを最大限に引き出します",
                            "CTA": "今すぐ購入",
                            "画像": ["image1.jpg", "image2.jpg"]
                        },
                        "配置": ["フィード", "ストーリー"]
                    },
                    {
                        "タイプ": "カルーセル広告",
                        "カード": [
                            {
                                "見出し": "商品A",
                                "説明": "商品Aの詳細説明",
                                "価格": 2980,
                                "画像": "productA.jpg"
                            },
                            {
                                "見出し": "商品B", 
                                "説明": "商品Bの詳細説明",
                                "価格": 3980,
                                "画像": "productB.jpg"
                            }
                        ]
                    }
                ]
            },
            "missing_fields": []
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(complex_data)
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "複雑な構造を持つ広告を作成してください。"},
                {"role": "assistant", "content": "複雑な構造を持つ広告を作成いたします。ネストした辞書、リスト、混合データ型を含む広告を準備しましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test Meta platform with complex structures
        result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Complex structure formatting successful!")
        print("📋 Hierarchical Structure Preview:")
        print("=" * 80)
        print(result['content'])
        print("=" * 80)
        
        # Analyze the hierarchical structure
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        print(f"\n🔍 Structure Analysis:")
        print(f"Total field lines: {len(field_lines)}")
        
        # Check indentation levels
        indent_levels = {}
        for line in field_lines:
            if ":" in line:
                field_part = line.split(":")[0]
                indent_count = (len(field_part) - len(field_part.lstrip())) // 2
                indent_levels[indent_count] = indent_levels.get(indent_count, 0) + 1
        
        print(f"Indentation levels found:")
        for level, count in sorted(indent_levels.items()):
            print(f"  Level {level}: {count} fields")
        
        # Show sample hierarchical fields
        print(f"\n📂 Sample Hierarchical Fields:")
        for i, line in enumerate(field_lines[:10]):
            field_name = line.split(":")[0].replace("■ ", "") if ":" in line else line
            field_value = line.split(":", 1)[1].strip() if ":" in line else ""
            indent_level = (len(line.split(":")[0]) - len(line.split(":")[0].lstrip())) // 2
            print(f"  Level {indent_level}: {field_name}")
            if field_value and len(field_value) > 50:
                print(f"    Value: {field_value[:50]}...")
            elif field_value:
                print(f"    Value: {field_value}")
        
        if len(field_lines) > 10:
            print(f"  ... and {len(field_lines) - 10} more fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Complex structure test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_list_with_dictionaries():
    """Test formatting of lists containing dictionaries."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("\n🔧 Testing Lists with Dictionaries")
        print("=" * 50)
        
        # Mock data with lists containing dictionaries
        list_dict_data = {
            "data": {
                "キャンペーン名": "リスト辞書テスト",
                "広告グループ": [
                    {
                        "名前": "グループA",
                        "予算": 10000,
                        "ターゲット": {
                            "年齢": "20-30",
                            "地域": ["東京", "大阪"]
                        }
                    },
                    {
                        "名前": "グループB",
                        "予算": 15000,
                        "ターゲット": {
                            "年齢": "30-40",
                            "地域": ["名古屋", "福岡"]
                        }
                    }
                ],
                "キーワード": [
                    ["美容", "スキンケア", "化粧品"],
                    ["健康", "サプリメント", "栄養"]
                ]
            },
            "missing_fields": []
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(list_dict_data)
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "リストと辞書を含む広告を作成してください。"},
                {"role": "assistant", "content": "リストと辞書を含む広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test the formatting
        result = await tool.execute(
            platform="Google Search Ads",
            conversation_context=conversation_context
        )
        
        print("✅ List-dictionary formatting successful!")
        print("📋 List-Dictionary Preview:")
        print("=" * 60)
        print(result['content'])
        print("=" * 60)
        
        # Check for proper list formatting
        content_lines = result['content'].split('\n')
        list_indicators = [line for line in content_lines if "[List with" in line or "Item " in line]
        
        print(f"\n📊 List Structure Analysis:")
        print(f"List structure indicators found: {len(list_indicators)}")
        
        for indicator in list_indicators[:5]:
            print(f"  • {indicator.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ List-dictionary test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_mixed_data_types():
    """Test formatting with all possible data type combinations."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Test the formatting methods directly
        tool = PreviewTool(Mock(), Mock())
        
        print("\n🔧 Testing Mixed Data Types")
        print("=" * 50)
        
        # Test complex mixed data
        mixed_data = {
            "文字列フィールド": "シンプルな文字列",
            "数値フィールド": 12345,
            "ブールフィールド": True,
            "リストフィールド": ["項目1", "項目2", "項目3"],
            "辞書フィールド": {
                "サブ文字列": "ネストした文字列",
                "サブ数値": 67890,
                "サブリスト": ["サブ項目1", "サブ項目2"]
            },
            "複雑リスト": [
                {
                    "オブジェクト1": "値1",
                    "ネスト": {
                        "深い値": "深くネストした値"
                    }
                },
                ["ネストしたリスト", "の中の項目"],
                "シンプルな項目"
            ]
        }
        
        print("📋 Mixed Data Types Preview:")
        print("-" * 40)
        
        # Test each field type
        for field_name, value in mixed_data.items():
            print(f"\n■ {field_name}:")
            formatted = tool._format_complex_field(field_name, value, {}, 0)
            
            if isinstance(formatted, dict):
                for sub_field, sub_value in formatted.items():
                    print(f"  {sub_field}: {sub_value}")
            else:
                print(f"  {formatted}")
        
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Mixed data types test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all hierarchical formatting tests."""
    print("🧪 Testing Hierarchical Structure Formatting")
    print("=" * 80)
    
    # Test 1: Complex nested structures
    complex_success = await test_complex_nested_structures()
    
    # Test 2: Lists with dictionaries
    list_dict_success = await test_list_with_dictionaries()
    
    # Test 3: Mixed data types
    mixed_success = await test_mixed_data_types()
    
    print(f"\n📊 Test Results:")
    print(f"Complex Nested Structures: {'✅ PASS' if complex_success else '❌ FAIL'}")
    print(f"Lists with Dictionaries: {'✅ PASS' if list_dict_success else '❌ FAIL'}")
    print(f"Mixed Data Types: {'✅ PASS' if mixed_success else '❌ FAIL'}")
    
    if complex_success and list_dict_success and mixed_success:
        print("\n🎉 All hierarchical formatting tests passed!")
        print("\n✅ Hierarchical formatting features:")
        print("• Handles any level of nesting (dicts, lists, mixed)")
        print("• Proper indentation with 2 spaces per level")
        print("• Clear structure indicators for complex objects")
        print("• Aligned items at the same hierarchical level")
        print("• Readable format for lists containing dictionaries")
        print("• Support for all data types (strings, numbers, booleans)")
        print("• Maintains ■ key: value format at all levels")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

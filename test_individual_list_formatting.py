#!/usr/bin/env python3
"""
Test script for individual list item formatting with character counts.

This script tests that each list item is formatted on a separate line
with individual character counts instead of being grouped together.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_individual_list_formatting():
    """Test that list items are formatted individually with character counts."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing Individual List Item Formatting")
        print("=" * 60)
        
        # Mock LLM response with various list types
        list_data = {
            "data": {
                "キャンペーン名": "リスト個別フォーマットテスト",
                "見出しリスト": [
                    "新商品登場！",
                    "美しさを引き出す革新的な商品",
                    "今すぐお試しください"
                ],
                "説明文リスト": [
                    "短い説明",
                    "これは少し長めの説明文です。商品の特徴を詳しく説明しています。",
                    "非常に長い説明文の例です。この商品は革新的な技術を使用しており、お客様の生活を豊かにする多くの機能を備えています。"
                ],
                "キーワードリスト": [
                    "美容",
                    "スキンケア",
                    "化粧品",
                    "アンチエイジング"
                ],
                "価格リスト": [1980, 2980, 3980, 4980],
                "ブールリスト": [True, False, True],
                "混合リスト": [
                    "文字列項目",
                    12345,
                    True,
                    "もう一つの文字列"
                ],
                "複雑リスト": [
                    {
                        "商品名": "商品A",
                        "価格": 1980,
                        "説明": "商品Aの詳細説明"
                    },
                    {
                        "商品名": "商品B",
                        "価格": 2980,
                        "説明": "商品Bの詳細説明"
                    }
                ]
            },
            "missing_fields": []
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(list_data)
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "様々なリスト形式を含む広告を作成してください。"},
                {"role": "assistant", "content": "様々なリスト形式を含む広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads (which has character limits)
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ Individual list formatting successful!")
        print("📋 Individual List Item Preview:")
        print("=" * 80)
        print(result['content'])
        print("=" * 80)
        
        # Analyze the list formatting
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        # Count different types of list items
        individual_items = [line for line in field_lines if " - Item " in line]
        char_count_items = [line for line in field_lines if "[" in line and "chars" in line]
        
        print(f"\n🔍 List Formatting Analysis:")
        print(f"Total field lines: {len(field_lines)}")
        print(f"Individual list items: {len(individual_items)}")
        print(f"Items with character counts: {len(char_count_items)}")
        
        # Show examples of different list types
        print(f"\n📝 Sample Individual List Items:")
        
        # Group by list type
        list_types = {}
        for line in individual_items:
            if ":" in line:
                field_part = line.split(":")[0].replace("■ ", "")
                list_name = field_part.split(" - Item ")[0] if " - Item " in field_part else field_part
                if list_name not in list_types:
                    list_types[list_name] = []
                list_types[list_name].append(line)
        
        for list_name, items in list_types.items():
            print(f"\n🔸 {list_name}:")
            for item in items[:3]:  # Show first 3 items
                field_name = item.split(":")[0].replace("■ ", "") if ":" in item else item
                field_value = item.split(":", 1)[1].strip() if ":" in item else ""
                item_number = field_name.split(" - Item ")[1] if " - Item " in field_name else "?"
                print(f"  Item {item_number}: {field_value}")
            
            if len(items) > 3:
                print(f"  ... and {len(items) - 3} more items")
        
        # Check for character count validation
        print(f"\n📊 Character Count Analysis:")
        char_validation_examples = []
        for line in char_count_items[:5]:
            field_name = line.split(":")[0].replace("■ ", "") if ":" in line else line
            field_value = line.split(":", 1)[1].strip() if ":" in line else ""
            
            # Extract character count info
            if "[" in field_value and "chars" in field_value:
                char_info = field_value[field_value.rfind("["):field_value.rfind("]")+1]
                actual_content = field_value[:field_value.rfind("[")].strip()
                char_validation_examples.append({
                    "field": field_name,
                    "content": actual_content,
                    "char_info": char_info
                })
        
        for example in char_validation_examples:
            print(f"  • {example['field']}:")
            print(f"    Content: {example['content']}")
            print(f"    Validation: {example['char_info']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Individual list formatting test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_list_processing():
    """Test the list processing method directly."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Create tool instance
        tool = PreviewTool(Mock(), Mock())
        
        print("\n🔧 Testing Direct List Processing")
        print("=" * 50)
        
        # Test different list types
        test_cases = [
            {
                "name": "Simple String List",
                "data": ["項目1", "項目2", "項目3"],
                "field_config": {"max_chars": 20}
            },
            {
                "name": "Mixed Type List", 
                "data": ["文字列", 123, True, "もう一つ"],
                "field_config": {"max_chars": 15}
            },
            {
                "name": "Long String List",
                "data": [
                    "短い",
                    "これは中程度の長さの文字列です",
                    "これは非常に長い文字列の例で、文字数制限を超える可能性があります"
                ],
                "field_config": {"max_chars": 25, "min_chars": 5}
            },
            {
                "name": "Number List",
                "data": [100, 200, 300, 400],
                "field_config": {}
            }
        ]
        
        for test_case in test_cases:
            print(f"\n📋 {test_case['name']}:")
            print(f"Input: {test_case['data']}")
            
            # Process the list
            extracted = tool._recursive_field_extraction(
                "テストリスト", 
                test_case['data'], 
                test_case['field_config']
            )
            
            print("Output:")
            for field_name, field_value in extracted.items():
                print(f"  ■ {field_name}: {field_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct list processing test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_before_after_comparison():
    """Show comparison between old grouped format and new individual format."""
    
    print("\n📊 Before vs After Comparison")
    print("=" * 60)
    
    print("\n🔴 Before (Grouped Format):")
    before_output = """■ 見出しリスト: 新商品登場！, 美しさを引き出す革新的な商品, 今すぐお試しください [67/max 100 chars ✓]
■ キーワードリスト: 美容, スキンケア, 化粧品, アンチエイジング [25/max 50 chars ✓]"""
    
    print(before_output)
    
    print("\n🟢 After (Individual Format):")
    after_output = """■ 見出しリスト - Item 1: 新商品登場！ [6/max 20 chars ✓]
■ 見出しリスト - Item 2: 美しさを引き出す革新的な商品 [17/max 20 chars ✓]
■ 見出しリスト - Item 3: 今すぐお試しください [10/max 20 chars ✓]
■ キーワードリスト - Item 1: 美容 [2/max 15 chars ✓]
■ キーワードリスト - Item 2: スキンケア [5/max 15 chars ✓]
■ キーワードリスト - Item 3: 化粧品 [3/max 15 chars ✓]
■ キーワードリスト - Item 4: アンチエイジング [8/max 15 chars ✓]"""
    
    print(after_output)
    
    print("\n✨ Benefits of Individual Formatting:")
    benefits = [
        "• Each list item has its own character count validation",
        "• Easy to identify which specific items exceed limits",
        "• Clear separation makes content easier to review",
        "• Individual items can be edited independently",
        "• Better alignment with Excel file structure",
        "• More precise content validation"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")

async def main():
    """Run all individual list formatting tests."""
    print("🧪 Testing Individual List Item Formatting")
    print("=" * 80)
    
    # Test 1: Individual list formatting
    individual_success = await test_individual_list_formatting()
    
    # Test 2: Direct list processing
    direct_success = test_direct_list_processing()
    
    # Show comparison
    show_before_after_comparison()
    
    print(f"\n📊 Test Results:")
    print(f"Individual List Formatting: {'✅ PASS' if individual_success else '❌ FAIL'}")
    print(f"Direct List Processing: {'✅ PASS' if direct_success else '❌ FAIL'}")
    
    if individual_success and direct_success:
        print("\n🎉 All individual list formatting tests passed!")
        print("\n✅ Individual list formatting features:")
        print("• Each list item appears on a separate line")
        print("• Individual character count validation for each item")
        print("• Clear Item 1, Item 2, Item 3 numbering")
        print("• Precise validation feedback per item")
        print("• Better content review and editing experience")
        print("• Maintains recursive processing for complex items")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

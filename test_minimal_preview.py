#!/usr/bin/env python3
"""
Minimal test for the PreviewTool to verify basic functionality.
"""

import asyncio
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_format_methods():
    """Test the formatting methods directly."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Create tool with mocked dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("✅ Tool created successfully")
        print(f"Tool name: {tool.name}")
        print(f"Tool description: {tool.description}")
        
        # Test platform display name method
        line_name = tool._get_platform_display_name("LINE Ads")
        meta_name = tool._get_platform_display_name("Meta (Instagram/Facebook)")
        youtube_name = tool._get_platform_display_name("YouTube Ads")
        
        print(f"\n📋 Platform Display Names:")
        print(f"LINE Ads -> {line_name}")
        print(f"Meta -> {meta_name}")
        print(f"YouTube -> {youtube_name}")
        
        # Test field finding method
        test_schema = {
            "キャンペーン名": {"description": "Campaign name", "max_chars": 30},
            "広告セット名": {"description": "Ad set name", "max_chars": 25},
            "年齢": {"description": "Age range"},
            "性別": {"description": "Gender", "options": ["全て", "男性", "女性"]},
            "エリア": {"description": "Area"}
        }
        
        campaign_field = tool._find_field_by_keywords(test_schema, ["キャンペーン", "campaign"])
        target_field = tool._find_field_by_keywords(test_schema, ["グループ", "広告セット", "ターゲット", "group", "set"])
        age_field = tool._find_field_by_keywords(test_schema, ["年齢", "age"])
        
        print(f"\n🔍 Field Finding:")
        print(f"Campaign field: {campaign_field}")
        print(f"Target field: {target_field}")
        print(f"Age field: {age_field}")
        
        # Test character range method
        char_range_1 = tool._get_char_range({"min_chars": 15, "max_chars": 30}, "default")
        char_range_2 = tool._get_char_range({"max_chars": 50}, "default")
        char_range_3 = tool._get_char_range({}, "10-25")
        
        print(f"\n📏 Character Ranges:")
        print(f"15-30 chars: {char_range_1}")
        print(f"Max 50 chars: {char_range_2}")
        print(f"Default range: {char_range_3}")
        
        # Test standardized fields creation
        standardized = tool._create_standardized_fields(test_schema)
        
        print(f"\n📝 Standardized Fields:")
        for field_name, description in standardized.items():
            print(f"■ {field_name}: [{description}]")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_streaming_generator():
    """Test the streaming generator method."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        tool = PreviewTool(Mock(), Mock())
        
        test_content = "LINE\n■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]\n■ ターゲット: [商品またはオーディエンス, 10–25 chars]"
        
        print("🌊 Testing streaming generator:")
        async for chunk in tool._create_stream_generator(test_content):
            print(f"Chunk: {repr(chunk)}")
        
        print("✅ Streaming generator test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Streaming test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Running minimal preview tool tests...\n")
    
    # Test basic methods
    basic_success = test_format_methods()
    
    if basic_success:
        # Test streaming
        streaming_success = asyncio.run(test_streaming_generator())
        
        if streaming_success:
            print("\n🎉 All minimal tests passed successfully!")
        else:
            print("\n❌ Streaming tests failed")
    else:
        print("\n❌ Basic tests failed")

if __name__ == "__main__":
    main()

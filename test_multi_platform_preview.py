#!/usr/bin/env python3
"""
Test script for the updated PreviewTool with multi-platform support.

This script tests that the preview tool correctly handles all platforms
by reading their JSON schema files and formatting output appropriately.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_multi_platform_support():
    """Test the preview tool with multiple platforms."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🌐 Testing Multi-Platform Preview Support")
        print("=" * 60)
        
        # Test platforms
        test_platforms = [
            "LINE Ads",
            "Meta (Instagram/Facebook)",
            "YouTube Ads",
            "Google Search Ads"
        ]
        
        # Mock conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい商品の広告を作成してください。"},
                {"role": "assistant", "content": "商品の広告を作成いたします。キャンペーン名、ターゲット、見出し、説明文などの広告コンテンツを準備しましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        for platform in test_platforms:
            print(f"\n🚀 Testing {platform}...")
            
            # Mock LLM response with sample data for this platform
            mock_response = Mock()
            mock_response.choices = [Mock()]
            
            # Create platform-specific mock data
            if platform == "LINE Ads":
                mock_data = {
                    "data": {
                        "キャンペーン": "テスト商品キャンペーン",
                        "グループ": "20代向けテスト商品",
                        "配信条件／配信ポイント": "20-30歳、東京エリア、興味関心：商品カテゴリ",
                        "年齢": "20-30",
                        "性別": "全て",
                        "エリア": "東京",
                        "見出し": "新商品登場",
                        "説明文": "革新的な商品をお試しください"
                    },
                    "missing_fields": []
                }
            elif platform == "Meta (Instagram/Facebook)":
                mock_data = {
                    "data": {
                        "キャンペーン名": "テスト商品Metaキャンペーン",
                        "キャンペーンの目的": "コンバージョン",
                        "広告セット名": ["商品セット1", "商品セット2"],
                        "配信条件／配信ポイント": ["20-30歳", "商品興味"],
                        "性別": "全て",
                        "年齢": "20-30",
                        "エリア": "日本全国",
                        "配置場所": "Meta全体",
                        "フィード広告・ストーリー広告": {
                            "見出し": "新商品で生活を豊かに",
                            "テキスト": "革新的な商品で、あなたの生活をより豊かにしましょう。",
                            "URL": "https://example.com/product"
                        }
                    },
                    "missing_fields": []
                }
            elif platform == "YouTube Ads":
                mock_data = {
                    "data": {
                        "キャンペーン名": "テスト商品YouTubeキャンペーン",
                        "キャンペーンの目的": "ブランド認知度とリーチ",
                        "広告グループ名": ["商品紹介動画", "商品デモ動画"],
                        "ターゲティング": "20-30歳、商品関連興味",
                        "年齢": "20-30",
                        "性別": "全て",
                        "地域": "日本",
                        "動画広告": {
                            "見出し": "新商品紹介",
                            "説明": "革新的な商品の魅力をご紹介",
                            "動画URL": "https://youtube.com/watch?v=example"
                        }
                    },
                    "missing_fields": []
                }
            else:  # Google Search Ads
                mock_data = {
                    "data": {
                        "キャンペーン名": "テスト商品検索キャンペーン",
                        "キャンペーンの目的": "ウェブサイトのトラフィック",
                        "広告グループ名": ["商品キーワード", "ブランドキーワード"],
                        "キーワード": ["新商品", "革新的商品", "商品名"],
                        "広告見出し1": "新商品登場",
                        "広告見出し2": "革新的な商品",
                        "広告見出し3": "今すぐお試し",
                        "説明文1": "革新的な商品で生活を豊かに",
                        "説明文2": "高品質な商品をお届け"
                    },
                    "missing_fields": []
                }
            
            mock_response.choices[0].message.content = json.dumps(mock_data)
            mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
            
            # Execute the tool
            result = await tool.execute(
                platform=platform,
                conversation_context=conversation_context
            )
            
            print(f"✅ {platform} execution successful!")
            
            # Display the preview content
            print(f"📋 {platform} Preview:")
            print("-" * 40)
            print(result['content'])
            print("-" * 40)
            
            # Analyze the output
            content_lines = result['content'].split('\n')
            platform_name = content_lines[0] if content_lines else ""
            field_lines = [line for line in content_lines[1:] if line.startswith("■")]
            
            print(f"   Platform name: {platform_name}")
            print(f"   Number of fields: {len(field_lines)}")
            
            # Show first few fields
            for i, field_line in enumerate(field_lines[:3]):
                field_name = field_line.split(":")[0].replace("■ ", "") if ":" in field_line else field_line
                print(f"   Field {i+1}: {field_name}")
            
            if len(field_lines) > 3:
                print(f"   ... and {len(field_lines) - 3} more fields")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-platform test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_nested_structure_handling():
    """Test handling of nested structures like Meta's carousel ads."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing Nested Structure Handling")
        print("=" * 50)
        
        # Mock LLM response with nested data
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン名": "ネストテストキャンペーン",
                "カルーセル広告": {
                    "カード1": {
                        "見出し": "商品1",
                        "説明": "商品1の説明"
                    },
                    "カード2": {
                        "見出し": "商品2", 
                        "説明": "商品2の説明"
                    }
                }
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "カルーセル広告を作成してください。"},
                {"role": "assistant", "content": "カルーセル広告を作成いたします。複数の商品カードを含む広告を準備しましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test Meta platform (which has nested structures)
        result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Nested structure test successful!")
        print("📋 Nested Structure Preview:")
        print("-" * 40)
        print(result['content'])
        print("-" * 40)
        
        # Check for nested field handling
        content_lines = result['content'].split('\n')
        nested_fields = [line for line in content_lines if line.startswith("  ■") or " - " in line]
        
        print(f"Found {len(nested_fields)} nested fields:")
        for nested_field in nested_fields[:5]:  # Show first 5
            print(f"  {nested_field}")
        
        return True
        
    except Exception as e:
        print(f"❌ Nested structure test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_missing_data_handling():
    """Test how the tool handles missing or incomplete data."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🧪 Testing Missing Data Handling")
        print("=" * 50)
        
        # Mock LLM response with minimal data
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "部分的キャンペーン",
                "年齢": "20-30"
                # Most fields missing
            },
            "missing_fields": ["グループ", "配信条件／配信ポイント", "性別", "エリア", "見出し", "説明文"]
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "広告を作成してください。"},
                {"role": "assistant", "content": "広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads with missing data
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ Missing data test successful!")
        print("📋 Preview with Missing Data:")
        print("-" * 40)
        print(result['content'])
        print("-" * 40)
        
        # Analyze placeholders vs actual data
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        placeholder_count = 0
        data_count = 0
        
        for line in field_lines:
            field_content = line.split(":", 1)[1].strip() if ":" in line else ""
            if field_content.startswith("[") and field_content.endswith("]"):
                placeholder_count += 1
            else:
                data_count += 1
        
        print(f"Fields with actual data: {data_count}")
        print(f"Fields with placeholders: {placeholder_count}")
        print(f"Total fields: {len(field_lines)}")
        
        if placeholder_count > 0:
            print("✅ Placeholders correctly shown for missing data")
        else:
            print("⚠️ No placeholders found")
        
        return True
        
    except Exception as e:
        print(f"❌ Missing data test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all multi-platform tests."""
    print("🧪 Testing Multi-Platform Preview Tool")
    print("=" * 70)
    
    # Test 1: Multi-platform support
    multi_platform_success = await test_multi_platform_support()
    
    # Test 2: Nested structure handling
    nested_success = await test_nested_structure_handling()
    
    # Test 3: Missing data handling
    missing_data_success = await test_missing_data_handling()
    
    print(f"\n📊 Test Results:")
    print(f"Multi-Platform Support: {'✅ PASS' if multi_platform_success else '❌ FAIL'}")
    print(f"Nested Structure Handling: {'✅ PASS' if nested_success else '❌ FAIL'}")
    print(f"Missing Data Handling: {'✅ PASS' if missing_data_success else '❌ FAIL'}")
    
    if multi_platform_success and nested_success and missing_data_success:
        print("\n🎉 All multi-platform tests passed!")
        print("\n✅ Updated preview tool features:")
        print("• Dynamically reads all fields from platform JSON schemas")
        print("• Supports all advertising platforms")
        print("• Handles nested structures (carousel ads, feed ads)")
        print("• Shows actual extracted data when available")
        print("• Provides appropriate placeholders for missing data")
        print("• Includes character count validation")
        print("• Maintains ■ key: value format for all platforms")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to verify that preview tool execution prevents follow-up responses.

This script tests that when the preview tool is executed and returns a streaming
response, the system does not generate a follow-up LLM response.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_no_followup_after_preview():
    """Test that no follow-up response is generated after preview tool execution."""
    try:
        from app.src.services.llm.core.stream_processor import StreamProcessor
        from app.src.services.llm.tools.tool_registry import ToolRegistry
        from app.src.services.llm.tools.preview_tool import PreviewTool
        from app.src.services.llm.core.error_handler import ErrorHandler
        
        # Mock dependencies
        mock_llm_client = Mock()
        mock_prompt_service = Mock()
        mock_error_handler = Mock()
        
        # Create tool registry and register preview tool
        tool_registry = ToolRegistry()
        preview_tool = PreviewTool(mock_prompt_service, mock_llm_client)
        tool_registry.register_tool(preview_tool)
        
        # Create stream processor
        stream_processor = StreamProcessor(
            llm_client=mock_llm_client,
            tool_registry=tool_registry,
            error_handler=mock_error_handler
        )
        
        print("✅ Stream processor created with preview tool registered")
        
        # Mock the initial streaming response that includes a tool call
        class MockChoice:
            def __init__(self, content=None, tool_calls=None, finish_reason=None):
                self.delta = Mock()
                self.delta.content = content
                self.delta.tool_calls = tool_calls
                self.finish_reason = finish_reason
        
        class MockChunk:
            def __init__(self, choice):
                self.choices = [choice]
        
        # Mock tool call for preview
        mock_tool_call_chunk = Mock()
        mock_tool_call_chunk.index = 0
        mock_tool_call_chunk.id = "call_123"
        mock_tool_call_chunk.function.name = "preview_ad_content"
        mock_tool_call_chunk.function.arguments = '{"platform": "LINE Ads"}'
        
        # Create mock streaming response
        async def mock_initial_stream():
            # Initial content
            yield MockChunk(MockChoice(content="プレビューを生成します。"))
            # Tool call
            yield MockChunk(MockChoice(tool_calls=[mock_tool_call_chunk]))
            # End
            yield MockChunk(MockChoice(finish_reason="tool_calls"))
        
        # Mock the LLM client's create_stream_completion method
        mock_llm_client.create_stream_completion = AsyncMock()
        mock_llm_client.create_stream_completion.side_effect = [
            mock_initial_stream(),  # Initial response with tool call
            # No second call should happen for follow-up
        ]
        
        # Test messages with advertising content
        messages = [
            {"role": "system", "content": "You are an advertising assistant."},
            {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
            {"role": "assistant", "content": "美容商品の広告を作成いたします。キャンペーン名、ターゲット、見出し、説明文などの広告コンテンツを準備しましょう。"},
            {"role": "user", "content": "プレビューを表示してください"}
        ]
        
        print("\n🚀 Testing stream processing with preview tool...")
        
        # Collect all output
        output_chunks = []
        call_count = 0
        
        # Track LLM client calls
        original_create_stream = mock_llm_client.create_stream_completion
        
        async def track_calls(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            print(f"📞 LLM Client call #{call_count}")
            if call_count == 1:
                return mock_initial_stream()
            else:
                # This should not happen if preview tool prevents follow-up
                print("❌ Unexpected follow-up call!")
                async def empty_stream():
                    yield MockChunk(MockChoice(content="This should not appear"))
                return empty_stream()
        
        mock_llm_client.create_stream_completion = AsyncMock(side_effect=track_calls)
        
        # Process the stream
        async for chunk in stream_processor.process_stream(
            messages=messages,
            max_tokens=4096,
            temperature=0
        ):
            output_chunks.append(chunk)
            print(f"📤 Output chunk: {repr(chunk)}")
        
        print(f"\n📊 Results:")
        print(f"Total LLM client calls: {call_count}")
        print(f"Total output chunks: {len(output_chunks)}")
        
        # Verify behavior
        if call_count == 1:
            print("✅ SUCCESS: Only one LLM call made (no follow-up response)")
        else:
            print(f"❌ FAILURE: Expected 1 LLM call, got {call_count}")
        
        # Check output content
        full_output = "".join(output_chunks)
        print(f"\n📋 Full output preview:")
        print(f"Length: {len(full_output)} characters")
        
        # Look for preview content
        if "LINE" in full_output and "キャンペーン名" in full_output:
            print("✅ Preview content found in output")
        else:
            print("❌ Preview content not found in output")
        
        # Check for unexpected follow-up content
        if "This should not appear" in full_output:
            print("❌ Unexpected follow-up content found!")
        else:
            print("✅ No unexpected follow-up content")
        
        return call_count == 1
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_normal_tool_still_gets_followup():
    """Test that non-preview tools still get follow-up responses."""
    try:
        from app.src.services.llm.core.stream_processor import StreamProcessor
        from app.src.services.llm.tools.tool_registry import ToolRegistry
        from app.src.services.llm.tools.excel_tool import ExcelTool
        from app.src.services.llm.core.error_handler import ErrorHandler
        
        # Mock dependencies
        mock_llm_client = Mock()
        mock_prompt_service = Mock()
        mock_error_handler = Mock()
        
        # Create tool registry and register Excel tool (non-preview)
        tool_registry = ToolRegistry()
        excel_tool = ExcelTool(mock_prompt_service, mock_llm_client)
        tool_registry.register_tool(excel_tool)
        
        # Create stream processor
        stream_processor = StreamProcessor(
            llm_client=mock_llm_client,
            tool_registry=tool_registry,
            error_handler=mock_error_handler
        )
        
        print("\n🔧 Testing normal tool (Excel) still gets follow-up...")
        
        # Mock Excel tool execution to return normal result
        async def mock_excel_execute(**kwargs):
            return {"download_url": "/test/file.xlsx"}
        
        excel_tool.execute = AsyncMock(side_effect=mock_excel_execute)
        
        # Mock streaming responses
        class MockChoice:
            def __init__(self, content=None, tool_calls=None, finish_reason=None):
                self.delta = Mock()
                self.delta.content = content
                self.delta.tool_calls = tool_calls
                self.finish_reason = finish_reason
        
        class MockChunk:
            def __init__(self, choice):
                self.choices = [choice]
        
        # Mock tool call for Excel
        mock_tool_call_chunk = Mock()
        mock_tool_call_chunk.index = 0
        mock_tool_call_chunk.id = "call_456"
        mock_tool_call_chunk.function.name = "create_excel_file"
        mock_tool_call_chunk.function.arguments = '{"platform": "LINE Ads"}'
        
        async def mock_initial_stream():
            yield MockChunk(MockChoice(content="Excelファイルを作成します。"))
            yield MockChunk(MockChoice(tool_calls=[mock_tool_call_chunk]))
            yield MockChunk(MockChoice(finish_reason="tool_calls"))
        
        async def mock_followup_stream():
            yield MockChunk(MockChoice(content="Excelファイルが作成されました。"))
        
        call_count = 0
        
        async def track_calls(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return mock_initial_stream()
            else:
                return mock_followup_stream()
        
        mock_llm_client.create_stream_completion = AsyncMock(side_effect=track_calls)
        
        # Test messages
        messages = [
            {"role": "system", "content": "You are an advertising assistant."},
            {"role": "user", "content": "Excelファイルを作成してください"}
        ]
        
        # Process the stream
        output_chunks = []
        async for chunk in stream_processor.process_stream(
            messages=messages,
            max_tokens=4096,
            temperature=0
        ):
            output_chunks.append(chunk)
        
        print(f"📊 Excel tool test results:")
        print(f"Total LLM client calls: {call_count}")
        
        if call_count == 2:
            print("✅ SUCCESS: Excel tool got follow-up response (2 LLM calls)")
            return True
        else:
            print(f"❌ FAILURE: Expected 2 LLM calls, got {call_count}")
            return False
        
    except Exception as e:
        print(f"❌ Excel tool test failed: {str(e)}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Testing Follow-up Response Prevention")
    print("=" * 50)
    
    # Test 1: Preview tool should NOT get follow-up
    preview_success = await test_no_followup_after_preview()
    
    # Test 2: Normal tools should still get follow-up
    excel_success = await test_normal_tool_still_gets_followup()
    
    print(f"\n📊 Final Results:")
    print(f"Preview tool (no follow-up): {'✅ PASS' if preview_success else '❌ FAIL'}")
    print(f"Excel tool (with follow-up): {'✅ PASS' if excel_success else '❌ FAIL'}")
    
    if preview_success and excel_success:
        print("\n🎉 All tests passed! Follow-up prevention working correctly.")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

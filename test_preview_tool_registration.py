#!/usr/bin/env python3
"""
Test script to verify that the preview tool is properly registered in the system.

This script tests that the preview tool is correctly registered and available
through the tool manager and LLM service.
"""

import asyncio
from unittest.mock import Mock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tool_manager_registration():
    """Test that the preview tool is registered in the tool manager."""
    try:
        from app.src.services.llm.managers.tool_manager import ToolManager
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_search_service = Mock()
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool manager with all dependencies
        tool_manager = ToolManager(
            search_service=mock_search_service,
            prompt_service=mock_prompt_service,
            llm_client=mock_llm_client
        )
        
        print("✅ Tool manager created successfully")
        
        # Get available tools
        available_tools = tool_manager.get_available_tools()
        print(f"📋 Available tools: {available_tools}")
        
        # Check if preview tool is registered
        if "preview_ad_content" in available_tools:
            print("✅ Preview tool is registered!")
        else:
            print("❌ Preview tool is NOT registered")
            return False
        
        # Get tool configurations
        tool_configs = tool_manager.get_tool_configs()
        preview_config = None
        
        for config in tool_configs:
            if config.get("function", {}).get("name") == "preview_ad_content":
                preview_config = config
                break
        
        if preview_config:
            print("✅ Preview tool configuration found")
            print(f"   Description: {preview_config['function']['description']}")
            print(f"   Parameters: {list(preview_config['function']['parameters']['properties'].keys())}")
        else:
            print("❌ Preview tool configuration NOT found")
            return False
        
        # Test getting the tool directly
        tool_registry = tool_manager.get_tool_registry()
        try:
            preview_tool = tool_registry.get_tool("preview_ad_content")
            print("✅ Preview tool can be retrieved from registry")
            print(f"   Tool type: {type(preview_tool).__name__}")
        except KeyError as e:
            print(f"❌ Cannot retrieve preview tool: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Tool manager test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_service_integration():
    """Test that the preview tool is available through the LLM service."""
    try:
        from app.src.services.llm_service import LLMService
        
        # Mock dependencies
        mock_search_service = Mock()
        mock_prompt_service = Mock()
        
        # Create LLM service
        llm_service = LLMService(
            search_service=mock_search_service,
            prompt_service=mock_prompt_service
        )
        
        print("\n✅ LLM service created successfully")
        
        # Get available tools through LLM service
        available_tools = llm_service.get_available_tools()
        print(f"📋 LLM service available tools: {available_tools}")
        
        # Check if preview tool is available
        if "preview_ad_content" in available_tools:
            print("✅ Preview tool is available through LLM service!")
        else:
            print("❌ Preview tool is NOT available through LLM service")
            return False
        
        # Get tool configurations through LLM service
        tool_configs = llm_service.get_tool_configs()
        preview_config = None
        
        for config in tool_configs:
            if config.get("function", {}).get("name") == "preview_ad_content":
                preview_config = config
                break
        
        if preview_config:
            print("✅ Preview tool configuration available through LLM service")
            
            # Verify the configuration structure
            function_config = preview_config.get("function", {})
            required_fields = ["name", "description", "parameters"]
            
            for field in required_fields:
                if field in function_config:
                    print(f"   ✅ {field}: Present")
                else:
                    print(f"   ❌ {field}: Missing")
                    return False
            
            # Check parameters structure
            parameters = function_config.get("parameters", {})
            if "properties" in parameters and "platform" in parameters["properties"]:
                print("   ✅ Platform parameter: Present")
                
                # Check enum values
                platform_enum = parameters["properties"]["platform"].get("enum", [])
                if "LINE Ads" in platform_enum:
                    print("   ✅ LINE Ads platform: Supported")
                else:
                    print("   ❌ LINE Ads platform: Not supported")
                    return False
            else:
                print("   ❌ Platform parameter: Missing")
                return False
        else:
            print("❌ Preview tool configuration NOT available through LLM service")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ LLM service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_configuration_details():
    """Test the detailed configuration of the preview tool."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        from unittest.mock import Mock
        
        # Create preview tool instance
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        preview_tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("\n🔧 Testing preview tool configuration details...")
        
        # Test tool properties
        print(f"Tool name: {preview_tool.name}")
        print(f"Tool description: {preview_tool.description}")
        
        # Test parameters
        parameters = preview_tool.parameters
        print(f"Parameters type: {parameters.get('type')}")
        
        properties = parameters.get("properties", {})
        platform_config = properties.get("platform", {})
        
        print(f"Platform parameter type: {platform_config.get('type')}")
        print(f"Platform enum values: {platform_config.get('enum', [])}")
        print(f"Required parameters: {parameters.get('required', [])}")
        
        # Test tool configuration for OpenAI
        config = preview_tool.get_config()
        print(f"OpenAI config type: {config.get('type')}")
        print(f"Function name: {config.get('function', {}).get('name')}")
        
        # Verify all required platforms are supported
        expected_platforms = [
            "LINE Ads",
            "Meta (Instagram/Facebook)",
            "YouTube Ads",
            "Google Search Ads",
            "Google Display Ads",
            "Google Demand Gen Ads",
            "P-Max",
            "Ad Extensions"
        ]
        
        supported_platforms = platform_config.get("enum", [])
        missing_platforms = []
        
        for platform in expected_platforms:
            if platform in supported_platforms:
                print(f"   ✅ {platform}: Supported")
            else:
                print(f"   ❌ {platform}: Missing")
                missing_platforms.append(platform)
        
        if not missing_platforms:
            print("✅ All expected platforms are supported")
            return True
        else:
            print(f"❌ Missing platforms: {missing_platforms}")
            return False
        
    except Exception as e:
        print(f"❌ Tool configuration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all registration tests."""
    print("🧪 Testing Preview Tool Registration")
    print("=" * 50)
    
    # Test 1: Tool Manager Registration
    tool_manager_success = test_tool_manager_registration()
    
    # Test 2: LLM Service Integration
    llm_service_success = test_llm_service_integration()
    
    # Test 3: Tool Configuration Details
    config_success = test_tool_configuration_details()
    
    print(f"\n📊 Registration Test Results:")
    print(f"Tool Manager Registration: {'✅ PASS' if tool_manager_success else '❌ FAIL'}")
    print(f"LLM Service Integration: {'✅ PASS' if llm_service_success else '❌ FAIL'}")
    print(f"Tool Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    
    if tool_manager_success and llm_service_success and config_success:
        print("\n🎉 All registration tests passed!")
        print("\n✅ Preview tool is properly registered and ready to use!")
        print("\nNext steps:")
        print("• The preview tool is now available for LLM function calling")
        print("• Users can call 'preview_ad_content' with platform parameter")
        print("• The tool will stream preview content directly to users")
        print("• No follow-up responses will be generated after preview")
    else:
        print("\n❌ Some registration tests failed. Check the implementation.")

if __name__ == "__main__":
    main()

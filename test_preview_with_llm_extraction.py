#!/usr/bin/env python3
"""
Test script for the updated PreviewTool with LLM extraction.

This script tests that the preview tool correctly extracts data from conversation
history using LLM and formats it in the requested format.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_preview_with_llm_extraction():
    """Test the preview tool with LLM data extraction."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Mock LLM response with extracted data
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "美容商品春キャンペーン",
                "グループ": "20代女性向け美容商品",
                "配信条件／配信ポイント": "20-30歳女性、東京・大阪エリア、美容・健康に興味のあるユーザー",
                "年齢": "20-30",
                "性別": "女性",
                "エリア": "東京・大阪",
                "見出し": "新商品登場！美しさを引き出す",
                "説明文": "革新的な美容商品で、あなたの美しさを最大限に引き出します。今すぐお試しください！"
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing preview tool with LLM extraction...")
        print(f"Tool name: {tool.name}")
        
        # Test conversation context with advertising content
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい美容商品の広告を作成してください。ターゲットは20代女性で、東京と大阪で配信したいです。"},
                {"role": "assistant", "content": "美容商品の広告を作成いたします。20代女性をターゲットとした「美容商品春キャンペーン」として、東京・大阪エリアで配信する広告を準備しましょう。見出しは「新商品登場！美しさを引き出す」、説明文は「革新的な美容商品で、あなたの美しさを最大限に引き出します」といった内容で進めます。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads
        print("\n🚀 Testing LINE Ads with extracted data...")
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ LINE Ads execution successful!")
        print(f"Response type: {result.get('type')}")
        print(f"Platform: {result.get('platform')}")
        print(f"Status: {result.get('extraction_status')}")
        
        # Display the formatted preview content
        print("\n📋 Generated Preview Content:")
        print("=" * 50)
        print(result['content'])
        print("=" * 50)
        
        # Verify the format
        content_lines = result['content'].split('\n')
        
        print(f"\n🔍 Format Verification:")
        
        # Check platform name
        if content_lines[0] == "LINE":
            print("✅ Platform name: Correct")
        else:
            print(f"❌ Platform name: Expected 'LINE', got '{content_lines[0]}'")
        
        # Check field format
        expected_fields = ["キャンペーン名", "ターゲット", "配信条件", "年齢層", "性別", "地域"]
        found_fields = []
        
        for line in content_lines[1:]:
            if line.startswith("■"):
                field_name = line.split(":")[0].replace("■ ", "")
                found_fields.append(field_name)
                
                # Check if field has actual data or placeholder
                field_content = line.split(":", 1)[1].strip() if ":" in line else ""
                if field_content.startswith("[") and field_content.endswith("]"):
                    print(f"⚠️  {field_name}: Placeholder format")
                else:
                    print(f"✅ {field_name}: Has extracted data")
        
        print(f"\nFound fields: {found_fields}")
        print(f"Expected fields: {expected_fields}")
        
        # Test Meta platform
        print("\n🚀 Testing Meta platform...")
        meta_result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Meta execution successful!")
        print("📋 Meta Preview (first 5 lines):")
        meta_lines = meta_result['content'].split('\n')[:5]
        for line in meta_lines:
            print(f"  {line}")
        
        # Test streaming functionality
        print(f"\n🌊 Testing streaming functionality...")
        if 'stream_generator' in result:
            print("Stream generator available, testing first few chunks:")
            chunk_count = 0
            async for chunk in result['stream_generator']:
                print(f"Chunk {chunk_count + 1}: {repr(chunk)}")
                chunk_count += 1
                if chunk_count >= 3:
                    print("... (remaining chunks)")
                    break
        else:
            print("❌ No stream generator found")
        
        # Verify LLM was called for extraction
        print(f"\n📞 LLM Call Verification:")
        if mock_llm_client.create_chat_completion.called:
            print("✅ LLM was called for data extraction")
            call_args = mock_llm_client.create_chat_completion.call_args
            messages = call_args[1]['messages'] if 'messages' in call_args[1] else call_args[0][0]
            print(f"   Messages sent to LLM: {len(messages)}")
            if messages and messages[0]['role'] == 'system':
                system_prompt = messages[0]['content']
                if "Extract all relevant data" in system_prompt:
                    print("✅ Extraction prompt detected")
                else:
                    print("❌ Extraction prompt not found")
        else:
            print("❌ LLM was not called")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_empty_data_handling():
    """Test how the tool handles empty or missing data."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Mock LLM response with minimal data
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "",
                "グループ": "",
                "年齢": "20-30",
                "性別": "女性",
                "エリア": ""
            },
            "missing_fields": ["キャンペーン", "グループ", "エリア"]
        })
        
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("\n🧪 Testing empty data handling...")
        
        # Test conversation context with minimal content
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "広告を作成してください。"},
                {"role": "assistant", "content": "広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ Empty data handling successful!")
        print("📋 Preview with minimal data:")
        print(result['content'])
        
        # Check that placeholders are shown for missing data
        content_lines = result['content'].split('\n')
        placeholder_count = 0
        data_count = 0
        
        for line in content_lines[1:]:
            if line.startswith("■"):
                field_content = line.split(":", 1)[1].strip() if ":" in line else ""
                if field_content.startswith("[") and field_content.endswith("]"):
                    placeholder_count += 1
                else:
                    data_count += 1
        
        print(f"Fields with placeholders: {placeholder_count}")
        print(f"Fields with data: {data_count}")
        
        if placeholder_count > 0:
            print("✅ Placeholders shown for missing data")
        else:
            print("❌ No placeholders found")
        
        return True
        
    except Exception as e:
        print(f"❌ Empty data test failed: {str(e)}")
        return False

async def main():
    """Run all tests."""
    print("🧪 Testing Preview Tool with LLM Extraction")
    print("=" * 60)
    
    # Test 1: Normal extraction with data
    extraction_success = await test_preview_with_llm_extraction()
    
    # Test 2: Empty data handling
    empty_data_success = await test_empty_data_handling()
    
    print(f"\n📊 Test Results:")
    print(f"LLM Extraction: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    print(f"Empty Data Handling: {'✅ PASS' if empty_data_success else '❌ FAIL'}")
    
    if extraction_success and empty_data_success:
        print("\n🎉 All tests passed!")
        print("\n✅ Preview tool now:")
        print("• Extracts real data from conversation using LLM")
        print("• Formats data in the requested text format")
        print("• Shows actual values when available")
        print("• Shows placeholders when data is missing")
        print("• Streams content directly to users")
        print("• Prevents follow-up responses")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

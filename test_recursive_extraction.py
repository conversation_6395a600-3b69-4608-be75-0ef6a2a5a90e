#!/usr/bin/env python3
"""
Test script for recursive field extraction in the PreviewTool.

This script tests that the recursive extraction properly extracts all
nested content until reaching actual string values.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_deep_recursive_extraction():
    """Test deep recursive extraction of nested structures."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("🔧 Testing Deep Recursive Extraction")
        print("=" * 60)
        
        # Create deeply nested test data
        deep_nested_data = {
            "data": {
                "キャンペーン名": "再帰テストキャンペーン",
                "レベル1": {
                    "レベル2A": {
                        "レベル3A": {
                            "レベル4A": "最深値A",
                            "レベル4B": 12345,
                            "レベル4C": True
                        },
                        "レベル3B": "中間値B"
                    },
                    "レベル2B": {
                        "レベル3C": [
                            {
                                "ネストオブジェクト1": {
                                    "深い文字列1": "深くネストした文字列1",
                                    "深い数値1": 999
                                }
                            },
                            {
                                "ネストオブジェクト2": {
                                    "深い文字列2": "深くネストした文字列2",
                                    "深いリスト": ["項目1", "項目2", "項目3"]
                                }
                            }
                        ]
                    }
                },
                "複雑リスト": [
                    {
                        "オブジェクトA": {
                            "サブオブジェクトA": {
                                "最終値A": "最終的な文字列A"
                            }
                        }
                    },
                    [
                        {
                            "ネストしたリスト内オブジェクト": "ネストした値"
                        },
                        "シンプルな文字列"
                    ],
                    "直接的な文字列"
                ],
                "混合データ": {
                    "文字列": "テスト文字列",
                    "数値": 42,
                    "ブール": False,
                    "リスト": ["a", "b", "c"],
                    "ネストした辞書": {
                        "さらにネスト": {
                            "最終文字列": "最も深い文字列"
                        }
                    }
                }
            },
            "missing_fields": []
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(deep_nested_data)
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "深くネストしたデータ構造を持つ広告を作成してください。"},
                {"role": "assistant", "content": "深くネストしたデータ構造を持つ広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test the recursive extraction
        result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Deep recursive extraction successful!")
        print("📋 Extracted Content:")
        print("=" * 80)
        print(result['content'])
        print("=" * 80)
        
        # Analyze the extraction depth
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        print(f"\n🔍 Extraction Analysis:")
        print(f"Total extracted fields: {len(field_lines)}")
        
        # Count nesting levels
        max_depth = 0
        depth_counts = {}
        
        for line in field_lines:
            if ":" in line:
                field_part = line.split(":")[0].replace("■ ", "")
                depth = field_part.count(" - ")
                max_depth = max(max_depth, depth)
                depth_counts[depth] = depth_counts.get(depth, 0) + 1
        
        print(f"Maximum nesting depth: {max_depth}")
        print(f"Fields by depth level:")
        for depth in sorted(depth_counts.keys()):
            print(f"  Level {depth}: {depth_counts[depth]} fields")
        
        # Show sample deep fields
        print(f"\n📂 Sample Deep Fields:")
        deep_fields = [line for line in field_lines if line.count(" - ") >= 3][:5]
        for field in deep_fields:
            field_name = field.split(":")[0].replace("■ ", "") if ":" in field else field
            field_value = field.split(":", 1)[1].strip() if ":" in field else ""
            print(f"  • {field_name}: {field_value}")
        
        # Verify specific deep extractions
        expected_deep_fields = [
            "レベル1 - レベル2A - レベル3A - レベル4A",
            "レベル1 - レベル2A - レベル3A - レベル4B", 
            "レベル1 - レベル2A - レベル3A - レベル4C",
            "複雑リスト - Item 1 - オブジェクトA - サブオブジェクトA - 最終値A",
            "混合データ - ネストした辞書 - さらにネスト - 最終文字列"
        ]
        
        found_fields = []
        for expected in expected_deep_fields:
            for line in field_lines:
                if expected in line:
                    found_fields.append(expected)
                    break
        
        print(f"\n✅ Deep Field Verification:")
        print(f"Expected deep fields: {len(expected_deep_fields)}")
        print(f"Found deep fields: {len(found_fields)}")
        
        for field in found_fields:
            print(f"  ✅ {field}")
        
        missing_fields = set(expected_deep_fields) - set(found_fields)
        for field in missing_fields:
            print(f"  ❌ Missing: {field}")
        
        return len(missing_fields) == 0
        
    except Exception as e:
        print(f"❌ Deep recursive extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_mixed_list_extraction():
    """Test extraction of lists with mixed content types."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        print("\n🔧 Testing Mixed List Extraction")
        print("=" * 50)
        
        # Test data with mixed list content
        mixed_list_data = {
            "data": {
                "キャンペーン名": "混合リストテスト",
                "混合リスト": [
                    "シンプルな文字列",
                    42,
                    True,
                    {
                        "オブジェクト内容": {
                            "ネストした値": "深い文字列"
                        }
                    },
                    [
                        "ネストしたリスト項目1",
                        {
                            "リスト内オブジェクト": "リスト内の値"
                        }
                    ]
                ],
                "シンプルリスト": ["項目1", "項目2", "項目3"],
                "数値リスト": [1, 2, 3, 4, 5],
                "複雑オブジェクトリスト": [
                    {
                        "名前": "オブジェクト1",
                        "詳細": {
                            "説明": "オブジェクト1の説明",
                            "値": 100
                        }
                    },
                    {
                        "名前": "オブジェクト2",
                        "詳細": {
                            "説明": "オブジェクト2の説明",
                            "値": 200
                        }
                    }
                ]
            },
            "missing_fields": []
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(mixed_list_data)
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "混合リストを含む広告を作成してください。"},
                {"role": "assistant", "content": "混合リストを含む広告を作成いたします。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test the extraction
        result = await tool.execute(
            platform="YouTube Ads",
            conversation_context=conversation_context
        )
        
        print("✅ Mixed list extraction successful!")
        print("📋 Mixed List Content:")
        print("=" * 60)
        print(result['content'])
        print("=" * 60)
        
        # Analyze list handling
        content_lines = result['content'].split('\n')
        field_lines = [line for line in content_lines[1:] if line.startswith("■")]
        
        # Check for different list types
        simple_lists = [line for line in field_lines if ", " in line and " - Item " not in line]
        complex_lists = [line for line in field_lines if " - Item " in line]
        
        print(f"\n📊 List Analysis:")
        print(f"Simple lists (comma-separated): {len(simple_lists)}")
        print(f"Complex lists (item-by-item): {len(complex_lists)}")
        
        print(f"\n📝 Simple Lists:")
        for simple_list in simple_lists[:3]:
            field_name = simple_list.split(":")[0].replace("■ ", "") if ":" in simple_list else simple_list
            field_value = simple_list.split(":", 1)[1].strip() if ":" in simple_list else ""
            print(f"  • {field_name}: {field_value}")
        
        print(f"\n📂 Complex List Items:")
        for complex_item in complex_lists[:5]:
            field_name = complex_item.split(":")[0].replace("■ ", "") if ":" in complex_item else complex_item
            field_value = complex_item.split(":", 1)[1].strip() if ":" in complex_item else ""
            print(f"  • {field_name}: {field_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mixed list extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_recursive_method_directly():
    """Test the recursive extraction method directly."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Create tool instance
        tool = PreviewTool(Mock(), Mock())
        
        print("\n🔧 Testing Recursive Method Directly")
        print("=" * 50)
        
        # Test complex nested structure
        test_data = {
            "レベル1": {
                "レベル2": {
                    "レベル3": "最終値",
                    "レベル3リスト": ["項目1", "項目2"]
                }
            },
            "リスト": [
                {
                    "オブジェクト": {
                        "深い値": "深くネストした値"
                    }
                },
                "シンプル値"
            ]
        }
        
        print("📋 Test Data:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        print(f"\n📤 Recursive Extraction Results:")
        for field_name, value in test_data.items():
            extracted = tool._recursive_field_extraction(field_name, value, {})
            print(f"\nField: {field_name}")
            for sub_field, sub_value in extracted.items():
                print(f"  ■ {sub_field}: {sub_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct method test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all recursive extraction tests."""
    print("🧪 Testing Recursive Field Extraction")
    print("=" * 80)
    
    # Test 1: Deep recursive extraction
    deep_success = await test_deep_recursive_extraction()
    
    # Test 2: Mixed list extraction
    mixed_success = await test_mixed_list_extraction()
    
    # Test 3: Direct method testing
    direct_success = test_recursive_method_directly()
    
    print(f"\n📊 Test Results:")
    print(f"Deep Recursive Extraction: {'✅ PASS' if deep_success else '❌ FAIL'}")
    print(f"Mixed List Extraction: {'✅ PASS' if mixed_success else '❌ FAIL'}")
    print(f"Direct Method Testing: {'✅ PASS' if direct_success else '❌ FAIL'}")
    
    if deep_success and mixed_success and direct_success:
        print("\n🎉 All recursive extraction tests passed!")
        print("\n✅ Recursive extraction features:")
        print("• Extracts all nested content until reaching string values")
        print("• Handles unlimited nesting depth")
        print("• Processes mixed data types (dicts, lists, primitives)")
        print("• Maintains clear hierarchical field naming")
        print("• Optimizes simple lists as comma-separated values")
        print("• Expands complex lists item by item")
        print("• Preserves all information from nested structures")
    else:
        print("\n❌ Some tests failed. Check implementation.")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script for the simplified PreviewTool implementation.

This script tests the simplified preview_ad_content tool functionality.
"""

import asyncio
from unittest.mock import Mock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_simplified_preview_tool():
    """Test the simplified PreviewTool functionality."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies (no LLM client needed now)
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        # Test tool properties
        print("🔧 Testing simplified tool properties...")
        print(f"Tool name: {tool.name}")
        print(f"Tool description: {tool.description}")
        supported_platforms = tool.parameters["properties"]["platform"]["enum"]
        print(f"Supported platforms: {', '.join(supported_platforms)}")
        
        # Test conversation context with advertising content
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
                {"role": "assistant", "content": "美容商品の広告を作成いたします。まず、キャンペーン名は「テスト商品キャンペーン」とし、ターゲットは20代女性、見出しは「新商品登場！」、説明文は「革新的な美容商品で、あなたの美しさを引き出します」といった内容で進めましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads
        print("\n🚀 Testing LINE Ads preview...")
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ LINE Ads execution successful!")
        print("📋 LINE Ads Preview:")
        print(result['preview_content'])
        print()
        
        # Test Meta platform
        print("🚀 Testing Meta platform preview...")
        meta_result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Meta execution successful!")
        print("📋 Meta Preview:")
        print(meta_result['preview_content'])
        print()
        
        # Test YouTube platform
        print("🚀 Testing YouTube platform preview...")
        youtube_result = await tool.execute(
            platform="YouTube Ads",
            conversation_context=conversation_context
        )
        
        print("✅ YouTube execution successful!")
        print("📋 YouTube Preview:")
        print(youtube_result['preview_content'])
        print()
        
        # Verify the format matches the expected pattern
        print("🔍 Format Verification:")
        expected_fields = ["キャンペーン名", "ターゲット", "配信条件", "年齢層", "性別", "地域"]
        
        for platform_name, result in [("LINE", result), ("Meta", meta_result), ("YouTube", youtube_result)]:
            lines = result['preview_content'].split('\n')
            print(f"\n{platform_name} format check:")
            
            # Check platform name is first line
            if lines[0] == platform_name:
                print(f"  ✅ Platform name: {lines[0]}")
            else:
                print(f"  ❌ Platform name: Expected '{platform_name}', got '{lines[0]}'")
            
            # Check field format
            field_lines = [line for line in lines[1:] if line.startswith("■")]
            for field_line in field_lines[:3]:  # Check first 3 fields
                if "■" in field_line and ":" in field_line and "[" in field_line and "]" in field_line:
                    field_name = field_line.split(":")[0].replace("■ ", "")
                    print(f"  ✅ Field format: {field_name}")
                else:
                    print(f"  ❌ Invalid field format: {field_line}")
        
        # Test error cases
        print("\n🧪 Testing error cases...")
        
        # Test unsupported platform
        try:
            await tool.execute(platform="Unsupported Platform", conversation_context=conversation_context)
            print("❌ Should have failed for unsupported platform")
        except Exception as e:
            print(f"✅ Correctly rejected unsupported platform")
        
        # Test missing advertising content
        try:
            empty_context = {"messages": [{"role": "user", "content": "Hello"}]}
            await tool.execute(platform="LINE Ads", conversation_context=empty_context)
            print("❌ Should have failed for missing advertising content")
        except Exception as e:
            print(f"✅ Correctly detected missing advertising content")
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simplified_preview_tool())

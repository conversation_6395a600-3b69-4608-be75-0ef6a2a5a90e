#!/usr/bin/env python3
"""
Test script for the streaming integration of PreviewTool.

This script tests that the preview tool's streaming response is properly
integrated into the main stream processing pipeline.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_stream_integration():
    """Test the streaming integration with the stream processor."""
    try:
        from app.src.services.llm.core.stream_processor import StreamProcessor
        from app.src.services.llm.tools.tool_registry import ToolRegistry
        from app.src.services.llm.tools.preview_tool import PreviewTool
        from app.src.services.llm.core.error_handler import ErrorHandler
        
        # Mock dependencies
        mock_llm_client = Mock()
        mock_prompt_service = Mock()
        mock_error_handler = Mock()
        
        # Create tool registry and register preview tool
        tool_registry = ToolRegistry()
        preview_tool = PreviewTool(mock_prompt_service, mock_llm_client)
        tool_registry.register_tool(preview_tool)
        
        # Create stream processor
        stream_processor = StreamProcessor(
            llm_client=mock_llm_client,
            tool_registry=tool_registry,
            error_handler=mock_error_handler
        )
        
        print("✅ Stream processor created successfully")
        print(f"Registered tools: {tool_registry.list_tools()}")
        
        # Test the tool execution method directly
        print("\n🔧 Testing tool execution...")
        
        # Mock tool call for preview tool
        tool_call = {
            "id": "test_call_123",
            "function": {
                "name": "preview_ad_content",
                "arguments": json.dumps({"platform": "LINE Ads"})
            }
        }
        
        # Mock messages with advertising content
        messages = [
            {"role": "system", "content": "You are an advertising assistant."},
            {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
            {"role": "assistant", "content": "美容商品の広告を作成いたします。キャンペーン名、ターゲット、見出し、説明文などの広告コンテンツを準備しましょう。"},
            {"role": "user", "content": "プレビューを表示してください"}
        ]
        
        # Execute the tool call
        result = await stream_processor._execute_tool_call(tool_call, messages)
        
        print("✅ Tool execution successful!")
        print(f"Result type: {result.get('type')}")
        print(f"Platform: {result.get('platform')}")
        print(f"Has stream generator: {'stream_generator' in result}")
        
        # Test streaming the result
        if result.get("type") == "stream_response" and "stream_generator" in result:
            print("\n🌊 Testing streaming output:")
            chunk_count = 0
            async for chunk in result["stream_generator"]:
                print(f"Chunk {chunk_count + 1}: {repr(chunk)}")
                chunk_count += 1
                if chunk_count >= 5:  # Show only first 5 chunks
                    print("... (remaining chunks)")
                    break
        
        # Test the complete content
        print(f"\n📋 Complete content preview:")
        content = result.get("content", "")
        lines = content.split('\n')
        for i, line in enumerate(lines[:8]):  # Show first 8 lines
            print(f"Line {i+1}: {line}")
        if len(lines) > 8:
            print("... (remaining lines)")
        
        # Verify the format
        print(f"\n🔍 Format verification:")
        if lines and lines[0] in ["LINE", "Meta", "YouTube"]:
            print(f"✅ Platform name correct: {lines[0]}")
        else:
            print(f"❌ Platform name incorrect: {lines[0] if lines else 'No content'}")
        
        field_lines = [line for line in lines[1:] if line.startswith("■")]
        print(f"✅ Found {len(field_lines)} field lines")
        
        # Test error handling
        print(f"\n🧪 Testing error handling...")
        
        # Test with invalid platform
        invalid_tool_call = {
            "id": "test_call_456",
            "function": {
                "name": "preview_ad_content",
                "arguments": json.dumps({"platform": "Invalid Platform"})
            }
        }
        
        try:
            await stream_processor._execute_tool_call(invalid_tool_call, messages)
            print("❌ Should have failed for invalid platform")
        except Exception as e:
            print(f"✅ Correctly handled invalid platform: {type(e).__name__}")
        
        print("\n🎉 Stream integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_mock_stream_processing():
    """Test mock stream processing to simulate the full pipeline."""
    try:
        print("\n🚀 Testing mock stream processing pipeline...")
        
        # Simulate the streaming response that would come from the stream processor
        async def mock_stream_with_tool():
            # Simulate initial LLM response
            yield "プレビューを生成しています..."
            
            # Simulate tool call detection and execution
            yield "\n"  # Newline before tool output
            
            # Simulate streaming tool response
            tool_content = "LINE\n■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]\n■ ターゲット: [商品またはオーディエンス, 10–25 chars]"
            lines = tool_content.split('\n')
            
            for line in lines:
                await asyncio.sleep(0.1)  # Simulate streaming delay
                yield line + '\n'
            
            yield "\n"  # Newline after tool output
            
            # Simulate final LLM response
            yield "プレビューが生成されました。内容をご確認ください。"
        
        print("📋 Mock streaming output:")
        async for chunk in mock_stream_with_tool():
            print(chunk, end='', flush=True)
        
        print("\n✅ Mock stream processing test completed!")
        
    except Exception as e:
        print(f"❌ Mock test failed: {str(e)}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all integration tests."""
    print("🧪 Running stream integration tests...\n")
    
    await test_stream_integration()
    await test_mock_stream_processing()
    
    print("\n🎉 All integration tests completed!")

if __name__ == "__main__":
    asyncio.run(main())

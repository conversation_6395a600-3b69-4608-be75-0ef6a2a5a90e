#!/usr/bin/env python3
"""
Test script for the streaming PreviewTool implementation.

This script tests the streaming preview_ad_content tool functionality.
"""

import asyncio
from unittest.mock import Mock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_streaming_preview_tool():
    """Test the streaming PreviewTool functionality."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        # Test tool properties
        print("🔧 Testing streaming tool properties...")
        print(f"Tool name: {tool.name}")
        print(f"Tool description: {tool.description}")
        
        # Test conversation context with advertising content
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
                {"role": "assistant", "content": "美容商品の広告を作成いたします。まず、キャンペーン名は「テスト商品キャンペーン」とし、ターゲットは20代女性、見出しは「新商品登場！」、説明文は「革新的な美容商品で、あなたの美しさを引き出します」といった内容で進めましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads streaming
        print("\n🚀 Testing LINE Ads streaming preview...")
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ LINE Ads execution successful!")
        print(f"Response type: {result.get('type')}")
        print(f"Platform: {result.get('platform')}")
        print(f"Status: {result.get('extraction_status')}")
        
        # Test the streaming content
        print("\n📋 Streaming Preview Content:")
        if 'stream_generator' in result:
            print("🌊 Streaming content:")
            async for chunk in result['stream_generator']:
                print(chunk, end='', flush=True)
            print()  # Add final newline
        else:
            print("📄 Static content:")
            print(result.get('content', 'No content'))
        
        # Test Meta platform streaming
        print("\n🚀 Testing Meta platform streaming...")
        meta_result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Meta execution successful!")
        print("📋 Meta Preview (first few chunks):")
        
        if 'stream_generator' in meta_result:
            chunk_count = 0
            async for chunk in meta_result['stream_generator']:
                print(f"Chunk {chunk_count + 1}: {repr(chunk)}")
                chunk_count += 1
                if chunk_count >= 3:  # Show only first 3 chunks
                    print("... (remaining chunks)")
                    break
        
        # Test the format verification
        print("\n🔍 Format Verification:")
        content = result.get('content', '')
        lines = content.split('\n')
        
        if lines and lines[0] == "LINE":
            print("✅ Platform name format correct")
        else:
            print(f"❌ Platform name format incorrect: {lines[0] if lines else 'No content'}")
        
        # Check field format
        field_lines = [line for line in lines[1:] if line.startswith("■")]
        if field_lines:
            print(f"✅ Found {len(field_lines)} field lines")
            for i, field_line in enumerate(field_lines[:3]):
                if "■" in field_line and ":" in field_line and "[" in field_line and "]" in field_line:
                    print(f"✅ Field {i+1} format correct")
                else:
                    print(f"❌ Field {i+1} format incorrect: {field_line}")
        else:
            print("❌ No field lines found")
        
        # Test error cases
        print("\n🧪 Testing error cases...")
        
        # Test unsupported platform
        try:
            await tool.execute(platform="Unsupported Platform", conversation_context=conversation_context)
            print("❌ Should have failed for unsupported platform")
        except Exception as e:
            print(f"✅ Correctly rejected unsupported platform")
        
        # Test missing advertising content
        try:
            empty_context = {"messages": [{"role": "user", "content": "Hello"}]}
            await tool.execute(platform="LINE Ads", conversation_context=empty_context)
            print("❌ Should have failed for missing advertising content")
        except Exception as e:
            print(f"✅ Correctly detected missing advertising content")
        
        print("\n🎉 All streaming tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_streaming_preview_tool())

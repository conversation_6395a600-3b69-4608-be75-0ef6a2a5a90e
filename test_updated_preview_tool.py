#!/usr/bin/env python3
"""
Test script for the updated PreviewTool implementation.

This script tests the preview_ad_content tool functionality with multiple platforms.
"""

import asyncio
import json
from unittest.mock import Mock, AsyncMock
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_updated_preview_tool():
    """Test the updated PreviewTool functionality."""
    try:
        from app.src.services.llm.tools.preview_tool import PreviewTool
        
        # Mock dependencies
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Mock LLM response for LINE Ads
        line_response = Mock()
        line_response.choices = [Mock()]
        line_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン": "テスト商品キャンペーン",
                "グループ": "20代女性向けテスト商品",
                "配信条件／配信ポイント": "20-30歳女性、東京・大阪エリア、美容・健康に興味",
                "年齢": "20-30",
                "性別": "女性",
                "エリア": "東京・大阪",
                "見出し": "新商品登場！",
                "説明文": "革新的な美容商品で、あなたの美しさを引き出します。今すぐお試しください！"
            },
            "missing_fields": []
        })
        
        # Mock LLM response for Meta
        meta_response = Mock()
        meta_response.choices = [Mock()]
        meta_response.choices[0].message.content = json.dumps({
            "data": {
                "キャンペーン名": "美容商品キャンペーン",
                "キャンペーンの目的": "コンバージョン",
                "広告セット名": ["美容商品セット1", "美容商品セット2"],
                "配信条件／配信ポイント": ["20-30歳女性、美容興味", "25-35歳女性、健康志向"],
                "性別": "女性",
                "年齢": "20-35",
                "エリア": "日本全国",
                "配置場所": "Meta全体",
                "フィード広告・ストーリー広告": {
                    "見出し": "革新的美容商品で美しさアップ",
                    "テキスト": "新しい美容商品で、あなたの美しさを最大限に引き出しましょう。今すぐお試しください！",
                    "URL": "https://example.com/beauty-product"
                }
            },
            "missing_fields": []
        })
        
        mock_llm_client.create_chat_completion = AsyncMock()
        
        # Create tool instance
        tool = PreviewTool(mock_prompt_service, mock_llm_client)
        
        # Test tool properties
        print("🔧 Testing updated tool properties...")
        print(f"Tool name: {tool.name}")
        print(f"Tool description: {tool.description}")
        supported_platforms = tool.parameters["properties"]["platform"]["enum"]
        print(f"Supported platforms: {', '.join(supported_platforms)}")
        
        # Test conversation context with advertising content
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are an advertising assistant."},
                {"role": "user", "content": "新しい美容商品の広告を作成してください。"},
                {"role": "assistant", "content": "美容商品の広告を作成いたします。まず、キャンペーン名は「テスト商品キャンペーン」とし、ターゲットは20代女性、見出しは「新商品登場！」、説明文は「革新的な美容商品で、あなたの美しさを引き出します」といった内容で進めましょう。"},
                {"role": "user", "content": "プレビューを表示してください"}
            ]
        }
        
        # Test LINE Ads
        print("\n🚀 Testing LINE Ads preview...")
        mock_llm_client.create_chat_completion.return_value = line_response
        
        result = await tool.execute(
            platform="LINE Ads",
            conversation_context=conversation_context
        )
        
        print("✅ LINE Ads execution successful!")
        print("📋 LINE Ads Preview:")
        print(result['preview_content'])
        
        # Test Meta platform
        print("\n🚀 Testing Meta platform preview...")
        mock_llm_client.create_chat_completion.return_value = meta_response
        
        meta_result = await tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        print("✅ Meta execution successful!")
        print("📋 Meta Preview:")
        print(meta_result['preview_content'])
        
        # Test error cases
        print("\n🧪 Testing error cases...")
        
        # Test unsupported platform
        try:
            await tool.execute(platform="Unsupported Platform", conversation_context=conversation_context)
            print("❌ Should have failed for unsupported platform")
        except Exception as e:
            print(f"✅ Correctly rejected unsupported platform")
        
        # Test missing advertising content
        try:
            empty_context = {"messages": [{"role": "user", "content": "Hello"}]}
            await tool.execute(platform="LINE Ads", conversation_context=empty_context)
            print("❌ Should have failed for missing advertising content")
        except Exception as e:
            print(f"✅ Correctly detected missing advertising content")
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_updated_preview_tool())

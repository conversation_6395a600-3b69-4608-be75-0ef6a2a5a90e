#!/usr/bin/env python3
"""
Simple verification script for preview tool registration.

This script verifies that the preview tool is properly registered without
requiring OpenAI API keys or external dependencies.
"""

from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_registration():
    """Verify that the preview tool is properly registered."""
    try:
        from app.src.services.llm.managers.tool_manager import ToolManager
        from app.src.services.llm.tools.preview_tool import PreviewTool
        from unittest.mock import Mock
        
        print("🔍 Verifying Preview Tool Registration")
        print("=" * 40)
        
        # Mock dependencies
        mock_search_service = Mock()
        mock_prompt_service = Mock()
        mock_llm_client = Mock()
        
        # Create tool manager
        tool_manager = ToolManager(
            search_service=mock_search_service,
            prompt_service=mock_prompt_service,
            llm_client=mock_llm_client
        )
        
        # Check available tools
        available_tools = tool_manager.get_available_tools()
        print(f"📋 Available tools: {available_tools}")
        
        # Verify preview tool is registered
        if "preview_ad_content" in available_tools:
            print("✅ Preview tool is registered!")
        else:
            print("❌ Preview tool is NOT registered")
            return False
        
        # Get tool instance
        tool_registry = tool_manager.get_tool_registry()
        preview_tool = tool_registry.get_tool("preview_ad_content")
        
        # Verify tool properties
        print(f"\n🔧 Tool Properties:")
        print(f"   Name: {preview_tool.name}")
        print(f"   Type: {type(preview_tool).__name__}")
        print(f"   Description: {preview_tool.description[:80]}...")
        
        # Verify tool configuration
        config = preview_tool.get_config()
        function_config = config.get("function", {})
        
        print(f"\n⚙️ Tool Configuration:")
        print(f"   Config type: {config.get('type')}")
        print(f"   Function name: {function_config.get('name')}")
        print(f"   Has parameters: {'parameters' in function_config}")
        
        # Verify platform support
        parameters = function_config.get("parameters", {})
        platform_enum = parameters.get("properties", {}).get("platform", {}).get("enum", [])
        
        print(f"\n🌐 Supported Platforms ({len(platform_enum)}):")
        for platform in platform_enum:
            print(f"   • {platform}")
        
        # Verify required parameters
        required = parameters.get("required", [])
        print(f"\n📝 Required Parameters: {required}")
        
        print(f"\n✅ Preview tool registration verification completed successfully!")
        print(f"\n📊 Summary:")
        print(f"   • Tool registered: ✅")
        print(f"   • Configuration valid: ✅")
        print(f"   • Platforms supported: {len(platform_enum)}")
        print(f"   • Ready for use: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_example():
    """Show example of how the preview tool will be used."""
    print(f"\n💡 Usage Example:")
    print(f"=" * 40)
    print(f"User: 'プレビューを表示してください'")
    print(f"System: Detects request and calls preview_ad_content tool")
    print(f"Tool: Generates streaming response with format:")
    print(f"""
LINE
■ キャンペーン名: [広告主 + 商品 + 目的, 15–30 chars]
■ ターゲット: [商品またはオーディエンス, 10–25 chars]
■ 配信条件: [年齢、性別、興味、地域など, 50–100 chars]
■ 年齢層: [18–65 or specific range]
■ 性別: [全て / 男性 / 女性]
■ 地域: [例: 日本全国; 不明な場合は「入力内容を再度確認してください。」]
""")
    print(f"Result: Content streams to user, no follow-up response generated")

def main():
    """Run verification and show usage example."""
    success = verify_registration()
    
    if success:
        show_usage_example()
        print(f"\n🎉 Preview tool is ready for production use!")
    else:
        print(f"\n❌ Preview tool registration needs to be fixed.")

if __name__ == "__main__":
    main()
